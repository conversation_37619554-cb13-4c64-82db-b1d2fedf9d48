package com.nacos.entity.po;

import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ProviderCapabilityPO 实体类测试
 */
public class ProviderCapabilityPOTest {

    @Test
    public void testBasicProperties() {
        ProviderCapabilityPO capability = new ProviderCapabilityPO();
        
        // 测试基本属性设置
        capability.setProviderCode("LINGYANG");
        capability.setLanguageCode("cn");
        capability.setProviderLanguageCode("zh-Hans");
        capability.setEnabled(1);
        
        LocalDateTime now = LocalDateTime.now();
        capability.setCreatedTime(now);
        capability.setUpdatedTime(now);
        
        // 验证属性
        assertEquals("LINGYANG", capability.getProviderCode());
        assertEquals("cn", capability.getLanguageCode());
        assertEquals("zh-Hans", capability.getProviderLanguageCode());
        assertEquals(Integer.valueOf(1), capability.getEnabled());
        assertEquals(now, capability.getCreatedTime());
        assertEquals(now, capability.getUpdatedTime());
        
        System.out.println("基本属性测试通过！");
    }

    @Test
    public void testEnabledStatus() {
        ProviderCapabilityPO capability = new ProviderCapabilityPO();
        
        // 测试启用状态
        capability.setEnabled(1);
        assertTrue(capability.isEnabledStatus());
        assertTrue(capability.supportsVideoTranslation());
        
        capability.setEnabled(0);
        assertFalse(capability.isEnabledStatus());
        assertFalse(capability.supportsVideoTranslation());
        
        capability.setEnabled(null);
        assertFalse(capability.isEnabledStatus());
        assertFalse(capability.supportsVideoTranslation());
        
        // 测试设置启用状态
        capability.setEnabledStatus(true);
        assertEquals(Integer.valueOf(1), capability.getEnabled());
        
        capability.setEnabledStatus(false);
        assertEquals(Integer.valueOf(0), capability.getEnabled());
        
        System.out.println("启用状态测试通过！");
    }

    @Test
    public void testBusinessMethods() {
        ProviderCapabilityPO capability = new ProviderCapabilityPO();
        capability.setLanguageCode("cn");
        capability.setProviderLanguageCode("zh-Hans");
        
        // 测试语言描述
        assertEquals("cn", capability.getLanguageDescription());
        assertEquals("zh-Hans", capability.getProviderLanguageDescription());
        
        // 测试有效语言代码
        assertEquals("zh-Hans", capability.getEffectiveLanguageCode());
        
        // 测试没有服务商代码的情况
        capability.setProviderLanguageCode(null);
        assertEquals("cn", capability.getProviderLanguageDescription());
        assertEquals("cn", capability.getEffectiveLanguageCode());
        
        // 测试语言匹配
        assertTrue(capability.matchesLanguage("cn"));
        assertFalse(capability.matchesLanguage("en"));
        assertFalse(capability.matchesLanguage(null));
        
        System.out.println("业务方法测试通过！");
    }

    @Test
    public void testToString() {
        ProviderCapabilityPO capability = new ProviderCapabilityPO();
        capability.setId(1L);
        capability.setProviderCode("LINGYANG");
        capability.setLanguageCode("cn");
        capability.setProviderLanguageCode("zh-Hans");
        capability.setEnabled(1);
        
        String result = capability.toString();
        assertTrue(result.contains("ProviderCapabilityPO"));
        assertTrue(result.contains("id=1"));
        assertTrue(result.contains("providerCode='LINGYANG'"));
        assertTrue(result.contains("languageCode='cn'"));
        assertTrue(result.contains("providerLanguageCode='zh-Hans'"));
        assertTrue(result.contains("enabled=1"));
        
        System.out.println("toString测试通过！");
        System.out.println("结果: " + result);
    }
}
