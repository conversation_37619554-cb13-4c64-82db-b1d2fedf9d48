package com.nacos.entity.po;

import com.nacos.entity.enums.UnifiedTaskStatusEnum;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import static org.junit.jupiter.api.Assertions.*;

/**
 * VideoTranslateTaskPO 状态迁移测试
 * 验证新的统一状态枚举是否正确工作
 * 
 * <AUTHOR>
 * @since 2025-08-01
 */
class VideoTranslateTaskPOTest {

    @Test
    @DisplayName("测试排队中状态")
    void testQueuingStatus() {
        VideoTranslateTaskPO task = new VideoTranslateTaskPO();
        task.setStatus(UnifiedTaskStatusEnum.QUEUING.getCode()); // 2
        
        assertTrue(task.isProcessing());
        assertFalse(task.isCompleted());
        assertFalse(task.isFailed());
        assertFalse(task.isCancelled());
        assertFalse(task.isFinalStatus());
        assertEquals("排队中", task.getStatusDescription());
        assertEquals(UnifiedTaskStatusEnum.QUEUING, task.getUnifiedStatus());
    }

    @Test
    @DisplayName("测试进行中状态")
    void testProgressStatus() {
        VideoTranslateTaskPO task = new VideoTranslateTaskPO();
        task.setStatus(UnifiedTaskStatusEnum.PROGRESS.getCode()); // 3
        
        assertTrue(task.isProcessing());
        assertFalse(task.isCompleted());
        assertFalse(task.isFailed());
        assertFalse(task.isCancelled());
        assertFalse(task.isFinalStatus());
        assertEquals("进行中", task.getStatusDescription());
        assertEquals(UnifiedTaskStatusEnum.PROGRESS, task.getUnifiedStatus());
    }

    @Test
    @DisplayName("测试成功状态")
    void testSuccessStatus() {
        VideoTranslateTaskPO task = new VideoTranslateTaskPO();
        task.setStatus(UnifiedTaskStatusEnum.SUCCESS.getCode()); // 1
        
        assertFalse(task.isProcessing());
        assertTrue(task.isCompleted());
        assertFalse(task.isFailed());
        assertFalse(task.isCancelled());
        assertTrue(task.isFinalStatus());
        assertEquals("翻译成功", task.getStatusDescription());
        assertEquals(UnifiedTaskStatusEnum.SUCCESS, task.getUnifiedStatus());
    }

    @Test
    @DisplayName("测试失败状态")
    void testFailedStatus() {
        VideoTranslateTaskPO task = new VideoTranslateTaskPO();
        task.setStatus(UnifiedTaskStatusEnum.FAILED.getCode()); // 0
        
        assertFalse(task.isProcessing());
        assertFalse(task.isCompleted());
        assertTrue(task.isFailed());
        assertFalse(task.isCancelled());
        assertTrue(task.isFinalStatus());
        assertEquals("失败", task.getStatusDescription());
        assertEquals(UnifiedTaskStatusEnum.FAILED, task.getUnifiedStatus());
    }

    @Test
    @DisplayName("测试超时状态")
    void testTimeoutStatus() {
        VideoTranslateTaskPO task = new VideoTranslateTaskPO();
        task.setStatus(UnifiedTaskStatusEnum.TIMEOUT.getCode()); // 4
        
        assertFalse(task.isProcessing());
        assertFalse(task.isCompleted());
        assertTrue(task.isFailed());
        assertFalse(task.isCancelled());
        assertTrue(task.isFinalStatus());
        assertEquals("超时", task.getStatusDescription());
        assertEquals(UnifiedTaskStatusEnum.TIMEOUT, task.getUnifiedStatus());
    }

    @Test
    @DisplayName("测试已取消状态")
    void testCancelledStatus() {
        VideoTranslateTaskPO task = new VideoTranslateTaskPO();
        task.setStatus(UnifiedTaskStatusEnum.CANCELLED.getCode()); // 5
        
        assertFalse(task.isProcessing());
        assertFalse(task.isCompleted());
        assertFalse(task.isFailed());
        assertTrue(task.isCancelled());
        assertTrue(task.isFinalStatus());
        assertEquals("已取消", task.getStatusDescription());
        assertEquals(UnifiedTaskStatusEnum.CANCELLED, task.getUnifiedStatus());
    }

    @Test
    @DisplayName("测试统一状态枚举便捷方法")
    void testUnifiedStatusMethods() {
        VideoTranslateTaskPO task = new VideoTranslateTaskPO();
        
        // 测试设置统一状态
        task.setUnifiedStatus(UnifiedTaskStatusEnum.QUEUING);
        assertEquals(UnifiedTaskStatusEnum.QUEUING.getCode(), task.getStatus());
        assertEquals(UnifiedTaskStatusEnum.QUEUING, task.getUnifiedStatus());
        
        // 测试状态转换检查
        assertTrue(task.canTransitionTo(UnifiedTaskStatusEnum.PROGRESS));
        assertTrue(task.canTransitionTo(UnifiedTaskStatusEnum.FAILED));
        assertTrue(task.canTransitionTo(UnifiedTaskStatusEnum.CANCELLED));
        assertFalse(task.canTransitionTo(UnifiedTaskStatusEnum.SUCCESS)); // 不能直接从排队转为成功
        
        // 测试显示样式
        assertEquals("processing", task.getStatusDisplayStyle());
        
        // 切换到成功状态
        task.setUnifiedStatus(UnifiedTaskStatusEnum.SUCCESS);
        assertEquals("success", task.getStatusDisplayStyle());
        
        // 最终状态不能再转换
        assertFalse(task.canTransitionTo(UnifiedTaskStatusEnum.FAILED));
    }

    @Test
    @DisplayName("测试状态转换规则")
    void testStatusTransitionRules() {
        VideoTranslateTaskPO task = new VideoTranslateTaskPO();
        
        // 排队中 -> 进行中
        task.setUnifiedStatus(UnifiedTaskStatusEnum.QUEUING);
        assertTrue(task.canTransitionTo(UnifiedTaskStatusEnum.PROGRESS));
        
        // 进行中 -> 成功
        task.setUnifiedStatus(UnifiedTaskStatusEnum.PROGRESS);
        assertTrue(task.canTransitionTo(UnifiedTaskStatusEnum.SUCCESS));
        assertTrue(task.canTransitionTo(UnifiedTaskStatusEnum.FAILED));
        assertTrue(task.canTransitionTo(UnifiedTaskStatusEnum.TIMEOUT));
        assertTrue(task.canTransitionTo(UnifiedTaskStatusEnum.CANCELLED));
        
        // 成功状态不能再转换
        task.setUnifiedStatus(UnifiedTaskStatusEnum.SUCCESS);
        assertFalse(task.canTransitionTo(UnifiedTaskStatusEnum.FAILED));
        assertFalse(task.canTransitionTo(UnifiedTaskStatusEnum.PROGRESS));
    }

    @Test
    @DisplayName("测试空状态处理")
    void testNullStatus() {
        VideoTranslateTaskPO task = new VideoTranslateTaskPO();
        task.setStatus(null);

        assertFalse(task.isProcessing());
        assertFalse(task.isCompleted());
        assertFalse(task.isFailed());
        assertFalse(task.isCancelled());
        assertFalse(task.isFinalStatus());
        assertEquals("未知状态", task.getStatusDescription());
        assertNull(task.getUnifiedStatus());
        assertEquals("default", task.getStatusDisplayStyle());
    }

    @Test
    @DisplayName("测试翻译类型设置和判断")
    void testTranslationType() {
        VideoTranslateTaskPO task = new VideoTranslateTaskPO();

        // 测试原声翻译
        task.setTranslationType(VideoTranslateTaskPO.TRANSLATION_TYPE_ORIGINAL);
        assertTrue(task.isOriginalVoiceTranslation());
        assertFalse(task.isVoiceLibraryTranslation());
        assertEquals("ORIGINAL_VOICE", task.getTranslationTypeCode());

        // 测试声音库翻译
        task.setTranslationType(VideoTranslateTaskPO.TRANSLATION_TYPE_VOICE_LIBRARY);
        assertFalse(task.isOriginalVoiceTranslation());
        assertTrue(task.isVoiceLibraryTranslation());
        assertEquals("VOICE_LIBRARY", task.getTranslationTypeCode());

        // 测试未知类型
        task.setTranslationType("未知类型");
        assertFalse(task.isOriginalVoiceTranslation());
        assertFalse(task.isVoiceLibraryTranslation());
        assertEquals("UNKNOWN", task.getTranslationTypeCode());
    }

    @Test
    @DisplayName("测试根据useOriginalVoice标志设置翻译类型")
    void testSetTranslationTypeByFlag() {
        VideoTranslateTaskPO task = new VideoTranslateTaskPO();

        // 测试原声翻译
        task.setTranslationTypeByFlag(true);
        assertEquals(VideoTranslateTaskPO.TRANSLATION_TYPE_ORIGINAL, task.getTranslationType());
        assertTrue(task.isOriginalVoiceTranslation());

        // 测试声音库翻译
        task.setTranslationTypeByFlag(false);
        assertEquals(VideoTranslateTaskPO.TRANSLATION_TYPE_VOICE_LIBRARY, task.getTranslationType());
        assertTrue(task.isVoiceLibraryTranslation());

        // 测试null值（默认为声音库翻译）
        task.setTranslationTypeByFlag(null);
        assertEquals(VideoTranslateTaskPO.TRANSLATION_TYPE_VOICE_LIBRARY, task.getTranslationType());
        assertTrue(task.isVoiceLibraryTranslation());
    }

    @Test
    @DisplayName("测试包含翻译类型的任务摘要")
    void testTaskSummaryWithTranslationType() {
        VideoTranslateTaskPO task = new VideoTranslateTaskPO();
        task.setTaskId("test-task-123");
        task.setTaskName("测试翻译任务");
        task.setStatus(UnifiedTaskStatusEnum.SUCCESS.getCode());
        task.setProvider("LINGYANG");
        task.setTranslationType(VideoTranslateTaskPO.TRANSLATION_TYPE_ORIGINAL);

        String summary = task.getTaskSummary();
        assertTrue(summary.contains("test-task-123"));
        assertTrue(summary.contains("测试翻译任务"));
        assertTrue(summary.contains("翻译成功"));
        assertTrue(summary.contains("LINGYANG"));
        assertTrue(summary.contains("原声翻译"));
    }

    @Test
    @DisplayName("测试翻译类型常量")
    void testTranslationTypeConstants() {
        assertEquals("原声翻译", VideoTranslateTaskPO.TRANSLATION_TYPE_ORIGINAL);
        assertEquals("声音库翻译", VideoTranslateTaskPO.TRANSLATION_TYPE_VOICE_LIBRARY);
    }
}
