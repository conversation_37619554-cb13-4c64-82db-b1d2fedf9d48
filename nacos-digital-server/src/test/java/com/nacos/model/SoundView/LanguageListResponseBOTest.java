package com.nacos.model.SoundView;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.nacos.model.SoundView.model.response.LanguageListResponseBO;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * LanguageListResponseBO JSON解析测试
 */
public class LanguageListResponseBOTest {

    @Test
    public void testJsonParsing() {
        // 模拟实际的API响应
        String jsonResponse = """
            {
                "code": 0,
                "message": "success",
                "result": [
                    {
                        "name": "中文",
                        "value": "cn",
                        "icon": "https://oss.lingyangplat.com/iiep-dev/fcb941627dc24c4b9f6f0ac1ec6eabc4.png",
                        "sort": 1
                    },
                    {
                        "name": "英文",
                        "value": "en",
                        "icon": "https://oss.lingyangplat.com/iiep-dev/another-icon.png",
                        "sort": 2
                    }
                ]
            }
            """;

        try {
            // 测试JSON解析
            LanguageListResponseBO result = JSON.parseObject(jsonResponse, 
                new TypeReference<LanguageListResponseBO>() {});

            // 验证解析结果
            assertNotNull(result);
            assertEquals(0, result.getCode());
            assertEquals("success", result.getMessage());
            assertTrue(result.isSuccess());
            
            // 验证语种列表
            assertNotNull(result.getResult());
            assertEquals(2, result.getResult().size());
            
            // 验证第一个语种
            LanguageListResponseBO.LanguageInfo firstLang = result.getResult().get(0);
            assertEquals("中文", firstLang.getName());
            assertEquals("cn", firstLang.getCode());
            assertEquals(1, firstLang.getSortOrder());
            
            // 验证第二个语种
            LanguageListResponseBO.LanguageInfo secondLang = result.getResult().get(1);
            assertEquals("英文", secondLang.getName());
            assertEquals("en", secondLang.getCode());
            assertEquals(2, secondLang.getSortOrder());
            
            // 测试业务方法
            assertEquals(2, result.getTotal());
            assertTrue(result.hasData());
            assertEquals("语种列表查询成功: 共2种语言", result.getSummary());
            
            // 测试查找方法
            LanguageListResponseBO.LanguageInfo foundLang = result.findLanguageByCode("cn");
            assertNotNull(foundLang);
            assertEquals("中文", foundLang.getName());
            
            LanguageListResponseBO.LanguageInfo notFoundLang = result.findLanguageByCode("fr");
            assertNull(notFoundLang);
            
            System.out.println("JSON解析测试通过！");
            
        } catch (Exception e) {
            fail("JSON解析失败: " + e.getMessage());
        }
    }

    @Test
    public void testErrorResponse() {
        String errorResponse = """
            {
                "code": 400,
                "message": "参数错误",
                "result": null
            }
            """;

        try {
            LanguageListResponseBO result = JSON.parseObject(errorResponse, 
                new TypeReference<LanguageListResponseBO>() {});

            assertNotNull(result);
            assertEquals(400, result.getCode());
            assertEquals("参数错误", result.getMessage());
            assertFalse(result.isSuccess());
            assertTrue(result.hasError());
            assertEquals("参数错误", result.getErrorMessage());
            assertFalse(result.hasData());
            assertEquals(0, result.getTotal());
            
            System.out.println("错误响应解析测试通过！");
            
        } catch (Exception e) {
            fail("错误响应解析失败: " + e.getMessage());
        }
    }
}
