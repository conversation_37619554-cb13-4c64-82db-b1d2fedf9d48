package com.nacos.service.processor.impl;

import com.nacos.entity.dto.VideoTranslateRequestDTO;
import com.nacos.entity.po.VideoTranslateTaskPO;
import com.nacos.service.processor.VideoTranslateProcessorFactory;
import com.nacos.service.processor.VideoTranslateProcessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * UniversalVideoTranslateProcessor处理器选择逻辑测试
 * 
 * 专门测试修改后的selectProviderByBusinessRules方法，
 * 验证根据useOriginalVoice参数选择不同处理器的逻辑是否正确。
 * 
 * <AUTHOR>
 * @since 2025-08-05
 */
@ExtendWith(MockitoExtension.class)
class UniversalVideoTranslateProcessorSelectionTest {

    @InjectMocks
    private UniversalVideoTranslateProcessor processor;

    @Mock
    private VideoTranslateProcessorFactory processorFactory;

    @Mock
    private ObjectMapper objectMapper;

    private VideoTranslateRequestDTO originalVoiceRequest;
    private VideoTranslateRequestDTO voiceLibraryRequest;

    @BeforeEach
    void setUp() {
        // 创建原声翻译请求
        originalVoiceRequest = new VideoTranslateRequestDTO();
        originalVoiceRequest.setUserId("test-user-123");
        originalVoiceRequest.setVideoUrl("https://example.com/test-video.mp4");
        originalVoiceRequest.setSourceLanguage("zh");
        originalVoiceRequest.setTargetLanguage("en");
        originalVoiceRequest.setUseOriginalVoice(true); // 原声翻译
        originalVoiceRequest.setTaskName("原声翻译测试");

        // 创建声音库翻译请求
        voiceLibraryRequest = new VideoTranslateRequestDTO();
        voiceLibraryRequest.setUserId("test-user-123");
        voiceLibraryRequest.setVideoUrl("https://example.com/test-video.mp4");
        voiceLibraryRequest.setSourceLanguage("zh");
        voiceLibraryRequest.setTargetLanguage("en");
        voiceLibraryRequest.setUseOriginalVoice(false); // 声音库翻译
        voiceLibraryRequest.setVoiceId("test-voice-123");
        voiceLibraryRequest.setTaskName("声音库翻译测试");
    }

    /**
     * 测试原声翻译模式选择LINGYANG处理器
     */
    @Test
    void testSelectProvider_OriginalVoice_SelectsLingyang() throws Exception {
        // Given
        VideoTranslateRequestDTO request = originalVoiceRequest;

        // When
        String selectedProvider = invokeSelectProviderByBusinessRules(request);

        // Then
        assertEquals("LINGYANG", selectedProvider, "原声翻译应选择LINGYANG处理器");
    }

    /**
     * 测试声音库翻译模式选择VOICE_LIBRARY处理器
     */
    @Test
    void testSelectProvider_VoiceLibrary_SelectsVoiceLibrary() throws Exception {
        // Given
        VideoTranslateRequestDTO request = voiceLibraryRequest;

        // When
        String selectedProvider = invokeSelectProviderByBusinessRules(request);

        // Then
        assertEquals("VOICE_LIBRARY", selectedProvider, "声音库翻译应选择VOICE_LIBRARY处理器");
    }

    /**
     * 测试useOriginalVoice为null时的默认行为（应选择声音库翻译）
     */
    @Test
    void testSelectProvider_NullUseOriginalVoice_SelectsVoiceLibrary() throws Exception {
        // Given
        VideoTranslateRequestDTO request = voiceLibraryRequest;
        request.setUseOriginalVoice(null); // null值

        // When
        String selectedProvider = invokeSelectProviderByBusinessRules(request);

        // Then
        assertEquals("VOICE_LIBRARY", selectedProvider, "useOriginalVoice为null时应选择VOICE_LIBRARY处理器");
    }

    /**
     * 测试useOriginalVoice为false时选择声音库翻译
     */
    @Test
    void testSelectProvider_FalseUseOriginalVoice_SelectsVoiceLibrary() throws Exception {
        // Given
        VideoTranslateRequestDTO request = voiceLibraryRequest;
        request.setUseOriginalVoice(false);

        // When
        String selectedProvider = invokeSelectProviderByBusinessRules(request);

        // Then
        assertEquals("VOICE_LIBRARY", selectedProvider, "useOriginalVoice为false时应选择VOICE_LIBRARY处理器");
    }

    /**
     * 测试不同的useOriginalVoice值组合
     */
    @Test
    void testSelectProvider_VariousUseOriginalVoiceValues() throws Exception {
        // 测试true值
        originalVoiceRequest.setUseOriginalVoice(Boolean.TRUE);
        assertEquals("LINGYANG", invokeSelectProviderByBusinessRules(originalVoiceRequest));

        // 测试false值
        voiceLibraryRequest.setUseOriginalVoice(Boolean.FALSE);
        assertEquals("VOICE_LIBRARY", invokeSelectProviderByBusinessRules(voiceLibraryRequest));

        // 测试null值（应该默认为声音库翻译）
        voiceLibraryRequest.setUseOriginalVoice(null);
        assertEquals("VOICE_LIBRARY", invokeSelectProviderByBusinessRules(voiceLibraryRequest));
    }

    /**
     * 测试处理器选择的日志记录（通过验证方法调用）
     */
    @Test
    void testSelectProvider_LoggingBehavior() throws Exception {
        // Given
        VideoTranslateRequestDTO originalRequest = originalVoiceRequest;
        VideoTranslateRequestDTO libraryRequest = voiceLibraryRequest;

        // When & Then - 验证不同请求类型都能正确选择处理器
        String originalProvider = invokeSelectProviderByBusinessRules(originalRequest);
        String libraryProvider = invokeSelectProviderByBusinessRules(libraryRequest);

        assertEquals("LINGYANG", originalProvider);
        assertEquals("VOICE_LIBRARY", libraryProvider);
    }

    /**
     * 测试边界情况 - 请求对象为null
     */
    @Test
    void testSelectProvider_NullRequest() throws Exception {
        // Given
        VideoTranslateRequestDTO request = null;

        // When & Then - 应该抛出异常或返回默认值
        assertThrows(Exception.class, () -> {
            invokeSelectProviderByBusinessRules(request);
        }, "null请求应该抛出异常");
    }

    /**
     * 测试处理器工厂获取处理器的行为
     */
    @Test
    void testProcessorFactoryIntegration() {
        // Given
        VideoTranslateProcessor mockLingyangProcessor = mock(VideoTranslateProcessor.class);
        VideoTranslateProcessor mockVoiceLibraryProcessor = mock(VideoTranslateProcessor.class);

        when(processorFactory.getProcessor("LINGYANG")).thenReturn(Optional.of(mockLingyangProcessor));
        when(processorFactory.getProcessor("VOICE_LIBRARY")).thenReturn(Optional.of(mockVoiceLibraryProcessor));

        // When
        Optional<VideoTranslateProcessor> lingyangResult = processorFactory.getProcessor("LINGYANG");
        Optional<VideoTranslateProcessor> voiceLibraryResult = processorFactory.getProcessor("VOICE_LIBRARY");

        // Then
        assertTrue(lingyangResult.isPresent(), "应该能获取到LINGYANG处理器");
        assertTrue(voiceLibraryResult.isPresent(), "应该能获取到VOICE_LIBRARY处理器");
        assertEquals(mockLingyangProcessor, lingyangResult.get());
        assertEquals(mockVoiceLibraryProcessor, voiceLibraryResult.get());
    }

    /**
     * 测试处理器不存在的情况
     */
    @Test
    void testProcessorFactoryNotFound() {
        // Given
        when(processorFactory.getProcessor("UNKNOWN")).thenReturn(Optional.empty());

        // When
        Optional<VideoTranslateProcessor> result = processorFactory.getProcessor("UNKNOWN");

        // Then
        assertFalse(result.isPresent(), "不存在的处理器应该返回空Optional");
    }

    // ==================== 辅助方法 ====================

    /**
     * 通过反射调用私有方法selectProviderByBusinessRules
     */
    private String invokeSelectProviderByBusinessRules(VideoTranslateRequestDTO request) throws Exception {
        Method method = UniversalVideoTranslateProcessor.class.getDeclaredMethod(
                "selectProviderByBusinessRules", VideoTranslateRequestDTO.class);
        method.setAccessible(true);
        return (String) method.invoke(processor, request);
    }

    /**
     * 创建模拟的任务PO对象
     */
    private VideoTranslateTaskPO createMockTaskPO() {
        VideoTranslateTaskPO taskPO = new VideoTranslateTaskPO();
        taskPO.setTaskId("test-task-123");
        taskPO.setUserId("test-user-123");
        taskPO.setStatus(2); // QUEUING状态
        return taskPO;
    }
}
