package com.nacos.service.processor.impl;

import com.nacos.entity.dto.VideoTranslateRequestDTO;
import com.nacos.result.Result;
import com.nacos.service.processor.VideoTranslateProcessor;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.multipart.MultipartFile;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * VoiceLibraryVideoTranslateProcessor单元测试
 * 
 * 测试声音库视频翻译处理器的各种场景：
 * 1. 正常流程测试
 * 2. 参数验证测试
 * 3. 异常情况测试
 * 4. 边界条件测试
 * 
 * <AUTHOR>
 * @since 2025-08-05
 */
@ExtendWith(MockitoExtension.class)
class VoiceLibraryVideoTranslateProcessorTest {

    @InjectMocks
    private VoiceLibraryVideoTranslateProcessor processor;

    private VideoTranslateRequestDTO validRequest;
    private String testUserId;

    @BeforeEach
    void setUp() {
        testUserId = "test-user-123";
        validRequest = createValidRequest();
    }

    /**
     * 测试正常的声音库翻译流程
     */
    @Test
    void testProcessRequest_Success() {
        // Given
        VideoTranslateRequestDTO request = createValidRequest();
        String userId = "test-user-123";

        // When
        Result<VideoTranslateProcessor.VideoTranslateResult> result = processor.processRequest(request, userId);

        // Then
        // 由于当前实现中的具体API调用都是模拟的，这里主要测试流程和参数验证
        // 实际项目中需要mock相关的API调用
        assertNotNull(result);
        // 注意：由于当前实现返回null（模拟实现），所以这里测试会失败
        // 在实际实现完成后，应该测试成功的情况
    }

    /**
     * 测试参数验证 - 用户ID为空
     */
    @Test
    void testProcessRequest_NullUserId() {
        // Given
        VideoTranslateRequestDTO request = createValidRequest();
        String userId = null;

        // When
        Result<VideoTranslateProcessor.VideoTranslateResult> result = processor.processRequest(request, userId);

        // Then
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertTrue(result.getMessage().contains("参数验证失败"));
    }

    /**
     * 测试参数验证 - 请求对象为空
     */
    @Test
    void testProcessRequest_NullRequest() {
        // Given
        VideoTranslateRequestDTO request = null;
        String userId = "test-user-123";

        // When
        Result<VideoTranslateProcessor.VideoTranslateResult> result = processor.processRequest(request, userId);

        // Then
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertTrue(result.getMessage().contains("参数验证失败"));
    }

    /**
     * 测试参数验证 - 音色ID为空（声音库翻译模式下必填）
     */
    @Test
    void testProcessRequest_EmptyVoiceId() {
        // Given
        VideoTranslateRequestDTO request = createValidRequest();
        request.setVoiceId(null); // 声音库翻译模式下音色ID不能为空
        String userId = "test-user-123";

        // When
        Result<VideoTranslateProcessor.VideoTranslateResult> result = processor.processRequest(request, userId);

        // Then
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertTrue(result.getMessage().contains("音色ID不能为空"));
    }

    /**
     * 测试参数验证 - 视频URL为空
     */
    @Test
    void testProcessRequest_EmptyVideoUrl() {
        // Given
        VideoTranslateRequestDTO request = createValidRequest();
        request.setVideoUrl(null);
        String userId = "test-user-123";

        // When
        Result<VideoTranslateProcessor.VideoTranslateResult> result = processor.processRequest(request, userId);

        // Then
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertTrue(result.getMessage().contains("参数验证失败"));
    }

    /**
     * 测试参数验证 - 源语言为空
     */
    @Test
    void testProcessRequest_EmptySourceLanguage() {
        // Given
        VideoTranslateRequestDTO request = createValidRequest();
        request.setSourceLanguage(null);
        String userId = "test-user-123";

        // When
        Result<VideoTranslateProcessor.VideoTranslateResult> result = processor.processRequest(request, userId);

        // Then
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertTrue(result.getMessage().contains("参数验证失败"));
    }

    /**
     * 测试参数验证 - 目标语言为空
     */
    @Test
    void testProcessRequest_EmptyTargetLanguage() {
        // Given
        VideoTranslateRequestDTO request = createValidRequest();
        request.setTargetLanguage(null);
        String userId = "test-user-123";

        // When
        Result<VideoTranslateProcessor.VideoTranslateResult> result = processor.processRequest(request, userId);

        // Then
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertTrue(result.getMessage().contains("参数验证失败"));
    }

    /**
     * 测试参数验证 - 源语言和目标语言相同
     */
    @Test
    void testProcessRequest_SameLanguage() {
        // Given
        VideoTranslateRequestDTO request = createValidRequest();
        request.setSourceLanguage("zh");
        request.setTargetLanguage("zh"); // 相同语言
        String userId = "test-user-123";

        // When
        Result<VideoTranslateProcessor.VideoTranslateResult> result = processor.processRequest(request, userId);

        // Then
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertTrue(result.getMessage().contains("参数验证失败"));
    }

    /**
     * 测试checkTaskStatus方法
     */
    @Test
    void testCheckTaskStatus_Success() {
        // Given
        String providerTaskId = "test-task-123";

        // When
        Result<VideoTranslateProcessor.VideoTranslateResult> result = processor.checkTaskStatus(providerTaskId);

        // Then
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertEquals("completed", result.getData().getStatus());
        assertEquals(100, result.getData().getProgress());
        assertEquals("VOICE_LIBRARY", result.getData().getProviderName());
    }

    /**
     * 测试checkTaskStatus方法 - 空任务ID
     */
    @Test
    void testCheckTaskStatus_EmptyTaskId() {
        // Given
        String providerTaskId = null;

        // When
        Result<VideoTranslateProcessor.VideoTranslateResult> result = processor.checkTaskStatus(providerTaskId);

        // Then
        assertNotNull(result);
        // 当前实现没有对空任务ID进行特殊处理，但应该考虑添加验证
    }

    /**
     * 测试getProviderName方法
     */
    @Test
    void testGetProviderName() {
        // When
        String providerName = processor.getProviderName();

        // Then
        assertEquals("VOICE_LIBRARY", providerName);
    }

    /**
     * 测试isSupported方法
     */
    @Test
    void testIsSupported() {
        // Test supported provider
        assertTrue(processor.isSupported("VOICE_LIBRARY"));
        assertTrue(processor.isSupported("voice_library"));
        assertTrue(processor.isSupported("Voice_Library"));

        // Test unsupported provider
        assertFalse(processor.isSupported("LINGYANG"));
        assertFalse(processor.isSupported("ALIYUN"));
        assertFalse(processor.isSupported(null));
        assertFalse(processor.isSupported(""));
    }

    /**
     * 测试isHealthy方法
     */
    @Test
    void testIsHealthy() {
        // When
        boolean isHealthy = processor.isHealthy();

        // Then
        assertTrue(isHealthy); // 当前实现总是返回true
    }

    /**
     * 测试getPriority方法
     */
    @Test
    void testGetPriority() {
        // When
        int priority = processor.getPriority();

        // Then
        assertEquals(10, priority); // 声音库翻译处理器优先级为10
    }

    // ==================== 辅助方法 ====================

    /**
     * 创建有效的请求对象
     */
    private VideoTranslateRequestDTO createValidRequest() {
        VideoTranslateRequestDTO request = new VideoTranslateRequestDTO();
        request.setUserId("test-user-123");
        request.setVideoUrl("https://example.com/test-video.mp4");
        request.setSourceLanguage("zh");
        request.setTargetLanguage("en");
        request.setVoiceId("test-voice-123");
        request.setTaskName("测试声音库翻译任务");
        request.setUseOriginalVoice(false); // 声音库翻译模式
        return request;
    }

    /**
     * 创建模拟的MultipartFile
     */
    private MultipartFile createMockMultipartFile() {
        MultipartFile mockFile = mock(MultipartFile.class);
        when(mockFile.getOriginalFilename()).thenReturn("test-video.mp4");
        when(mockFile.getSize()).thenReturn(1024L);
        when(mockFile.isEmpty()).thenReturn(false);
        return mockFile;
    }
}
