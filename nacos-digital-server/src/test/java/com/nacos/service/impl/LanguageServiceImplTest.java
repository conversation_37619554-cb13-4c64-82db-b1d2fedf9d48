package com.nacos.service.impl;

import com.nacos.entity.vo.LanguageVO;
import com.nacos.result.Result;
import com.nacos.service.LanguageService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * LanguageService 测试类
 */
@SpringBootTest
@ActiveProfiles("test")
public class LanguageServiceImplTest {

    @Resource
    private LanguageService languageService;

    @Test
    public void testGetSupportedLanguages() {
        try {
            // 测试获取所有支持的语种
            Result<List<LanguageVO>> result = languageService.getSupportedLanguages(null, false);
            
            assertNotNull(result);
            assertTrue(result.isSuccess());
            assertNotNull(result.getData());
            
            System.out.println("支持的语种数量: " + result.getData().size());
            
            // 验证语种信息
            for (LanguageVO vo : result.getData()) {
                assertNotNull(vo.getCode());
                assertNotNull(vo.getName());
                assertNotNull(vo.getDisplayName());
                System.out.println(String.format("语种: %s (%s) - 服务商: %s", 
                    vo.getName(), vo.getCode(), vo.getSupportedProvidersDescription()));
            }
            
        } catch (Exception e) {
            fail("测试失败: " + e.getMessage());
        }
    }

    @Test
    public void testGetPopularLanguages() {
        try {
            // 测试获取热门语种
            Result<List<LanguageVO>> result = languageService.getPopularLanguages(5);
            
            assertNotNull(result);
            assertTrue(result.isSuccess());
            assertNotNull(result.getData());
            assertTrue(result.getData().size() <= 5);
            
            System.out.println("热门语种数量: " + result.getData().size());
            
            // 验证都是热门语种
            for (LanguageVO vo : result.getData()) {
                assertTrue(Boolean.TRUE.equals(vo.getIsPopular()));
                System.out.println(String.format("热门语种: %s (%s)", vo.getName(), vo.getCode()));
            }
            
        } catch (Exception e) {
            fail("测试失败: " + e.getMessage());
        }
    }



    @Test
    public void testCheckLanguagePairSupport() {
        try {
            // 测试检查语言对支持
            Result<Boolean> result = languageService.checkLanguagePairSupport("cn", "en");
            
            assertNotNull(result);
            assertTrue(result.isSuccess());
            assertNotNull(result.getData());
            
            System.out.println("中文->英文翻译支持: " + result.getData());
            
            // 测试相同语言（应该返回错误）
            Result<Boolean> sameLanguageResult = languageService.checkLanguagePairSupport("cn", "cn");
            assertFalse(sameLanguageResult.isSuccess());
            
            // 测试空参数（应该返回错误）
            Result<Boolean> emptyResult = languageService.checkLanguagePairSupport("", "en");
            assertFalse(emptyResult.isSuccess());
            
        } catch (Exception e) {
            fail("测试失败: " + e.getMessage());
        }
    }

    @Test
    public void testLanguageVOBusinessMethods() {
        // 测试LanguageVO的业务方法
        LanguageVO vo = new LanguageVO();
        vo.setCode("cn");
        vo.setName("中文");
        vo.setEnglishName("Chinese");
        vo.setSupportedProviders(List.of("LINGYANG", "AZURE"));
        vo.setIsPopular(true);
        
        // 测试显示名称
        assertEquals("中文", vo.getDisplayName());
        
        // 测试服务商支持
        assertTrue(vo.supportsProvider("LINGYANG"));
        assertFalse(vo.supportsProvider("UNKNOWN"));
        
        // 测试服务商数量
        assertEquals(2, vo.getProviderCount());
        assertTrue(vo.hasMultipleProviders());
        
        // 测试服务商描述
        assertEquals("LINGYANG、AZURE", vo.getSupportedProvidersDescription());
        
        System.out.println("LanguageVO业务方法测试通过");
    }
}
