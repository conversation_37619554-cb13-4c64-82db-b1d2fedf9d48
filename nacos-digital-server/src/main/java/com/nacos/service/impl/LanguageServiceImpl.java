package com.nacos.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nacos.entity.po.ProviderCapabilityPO;
import com.nacos.entity.vo.LanguageVO;
import com.nacos.mapper.ProviderCapabilityMapper;
import com.nacos.result.Result;
import com.nacos.service.LanguageService;
import com.nacos.service.LanguageSupportSyncService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 语种服务实现类
 * 提供视频翻译语种支持相关的业务逻辑
 *
 * <AUTHOR>
 * @since 2025-01-05
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LanguageServiceImpl implements LanguageService {

    private final ProviderCapabilityMapper providerCapabilityMapper;
    private final LanguageSupportSyncService languageSupportSyncService;

    /**
     * 热门语种代码列表（按使用频率排序）
     */
    private static final List<String> POPULAR_LANGUAGE_CODES = Arrays.asList(
        "cn", "en", "ja", "ko", "fr", "de", "es", "ru", "it", "pt"
    );

    /**
     * 语种名称映射
     */
    private static final Map<String, String> LANGUAGE_NAMES = createLanguageNamesMap();

    /**
     * 语种英文名称映射
     */
    private static final Map<String, String> LANGUAGE_ENGLISH_NAMES = createLanguageEnglishNamesMap();

    /**
     * 创建语种名称映射
     */
    private static Map<String, String> createLanguageNamesMap() {
        Map<String, String> map = new HashMap<>();
        map.put("cn", "中文");
        map.put("en", "英文");
        map.put("ja", "日文");
        map.put("ko", "韩文");
        map.put("fr", "法文");
        map.put("de", "德文");
        map.put("es", "西班牙文");
        map.put("ru", "俄文");
        map.put("it", "意大利文");
        map.put("pt", "葡萄牙文");
        map.put("ar", "阿拉伯文");
        map.put("th", "泰文");
        map.put("vi", "越南文");
        map.put("hi", "印地文");
        map.put("ms", "马来文");
        return Collections.unmodifiableMap(map);
    }

    /**
     * 创建语种英文名称映射
     */
    private static Map<String, String> createLanguageEnglishNamesMap() {
        Map<String, String> map = new HashMap<>();
        map.put("cn", "Chinese");
        map.put("en", "English");
        map.put("ja", "Japanese");
        map.put("ko", "Korean");
        map.put("fr", "French");
        map.put("de", "German");
        map.put("es", "Spanish");
        map.put("ru", "Russian");
        map.put("it", "Italian");
        map.put("pt", "Portuguese");
        map.put("ar", "Arabic");
        map.put("th", "Thai");
        map.put("vi", "Vietnamese");
        map.put("hi", "Hindi");
        map.put("ms", "Malay");
        return Collections.unmodifiableMap(map);
    }

    @Override
    public Result<List<LanguageVO>> getSupportedLanguages(String providerCode, Boolean popular) {
        String methodName = "getSupportedLanguages";
        log.info("[{}] 获取支持的语种列表, providerCode: {}, popular: {}", 
                methodName, providerCode, popular);

        try {
            // 构建查询条件
            LambdaQueryWrapper<ProviderCapabilityPO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ProviderCapabilityPO::getEnabled, 1);
            
            if (StringUtils.hasText(providerCode)) {
                queryWrapper.eq(ProviderCapabilityPO::getProviderCode, providerCode);
            }

            // 查询数据库
            List<ProviderCapabilityPO> capabilities = providerCapabilityMapper.selectList(queryWrapper);
            
            if (capabilities.isEmpty()) {
                log.warn("[{}] 未找到支持的语种", methodName);
                return Result.SUCCESS(new ArrayList<>());
            }

            // 按语种代码分组，收集支持的服务商
            Map<String, List<String>> languageProviderMap = capabilities.stream()
                .collect(Collectors.groupingBy(
                    ProviderCapabilityPO::getLanguageCode,
                    Collectors.mapping(ProviderCapabilityPO::getProviderCode, Collectors.toList())
                ));

            // 转换为VO对象
            List<LanguageVO> languageVOs = languageProviderMap.entrySet().stream()
                .map(entry -> convertToLanguageVO(entry.getKey(), entry.getValue()))
                .collect(Collectors.toList());

            // 过滤热门语种
            if (Boolean.TRUE.equals(popular)) {
                languageVOs = languageVOs.stream()
                    .filter(vo -> Boolean.TRUE.equals(vo.getIsPopular()))
                    .collect(Collectors.toList());
            }

            // 排序：热门语种优先，然后按排序权重
            languageVOs.sort((a, b) -> {
                // 热门语种优先
                if (!Objects.equals(a.getIsPopular(), b.getIsPopular())) {
                    return Boolean.TRUE.equals(b.getIsPopular()) ? 1 : -1;
                }
                // 按排序权重
                int orderA = a.getSortOrder() != null ? a.getSortOrder() : Integer.MAX_VALUE;
                int orderB = b.getSortOrder() != null ? b.getSortOrder() : Integer.MAX_VALUE;
                return Integer.compare(orderA, orderB);
            });

            log.info("[{}] 获取语种列表成功, 共{}种语言", methodName, languageVOs.size());
            return Result.SUCCESS(languageVOs);

        } catch (Exception e) {
            log.error("[{}] 获取语种列表异常", methodName, e);
            return Result.ERROR("获取语种列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<LanguageVO>> getPopularLanguages(Integer limit) {
        String methodName = "getPopularLanguages";
        log.info("[{}] 获取热门语种列表, limit: {}", methodName, limit);

        try {
            // 获取所有支持的语种
            Result<List<LanguageVO>> allLanguagesResult = getSupportedLanguages(null, false);
            if (!allLanguagesResult.isSuccess()) {
                return allLanguagesResult;
            }

            List<LanguageVO> allLanguages = allLanguagesResult.getData();
            
            // 过滤热门语种并限制数量
            List<LanguageVO> popularLanguages = allLanguages.stream()
                .filter(vo -> Boolean.TRUE.equals(vo.getIsPopular()))
                .limit(limit != null ? limit : 10)
                .collect(Collectors.toList());

            log.info("[{}] 获取热门语种列表成功, 共{}种语言", methodName, popularLanguages.size());
            return Result.SUCCESS(popularLanguages);

        } catch (Exception e) {
            log.error("[{}] 获取热门语种列表异常", methodName, e);
            return Result.ERROR("获取热门语种列表失败: " + e.getMessage());
        }
    }



    @Override
    public Result<Boolean> checkLanguagePairSupport(String sourceLanguage, String targetLanguage) {
        String methodName = "checkLanguagePairSupport";
        log.info("[{}] 检查语言对支持, sourceLanguage: {}, targetLanguage: {}", 
                methodName, sourceLanguage, targetLanguage);

        try {
            if (!StringUtils.hasText(sourceLanguage) || !StringUtils.hasText(targetLanguage)) {
                return Result.ERROR("源语言和目标语言代码不能为空");
            }

            if (sourceLanguage.equals(targetLanguage)) {
                return Result.ERROR("源语言和目标语言不能相同");
            }

            // 使用现有的语言对支持检查方法
            boolean isSupported = languageSupportSyncService.isLanguagePairSupported(null, sourceLanguage, targetLanguage);

            log.info("[{}] 语言对支持检查完成, 结果: {}", methodName, isSupported);
            return Result.SUCCESS(isSupported);

        } catch (Exception e) {
            log.error("[{}] 检查语言对支持异常", methodName, e);
            return Result.ERROR("检查语言对支持失败: " + e.getMessage());
        }
    }

    /**
     * 将语种代码和支持的服务商列表转换为LanguageVO对象
     */
    private LanguageVO convertToLanguageVO(String languageCode, List<String> supportedProviders) {
        LanguageVO vo = new LanguageVO();
        vo.setCode(languageCode);
        vo.setName(LANGUAGE_NAMES.getOrDefault(languageCode, languageCode));
        vo.setEnglishName(LANGUAGE_ENGLISH_NAMES.getOrDefault(languageCode, languageCode));
        vo.setSupportedProviders(supportedProviders);
        vo.setIsPopular(POPULAR_LANGUAGE_CODES.contains(languageCode));
        
        // 设置排序权重（热门语种按在列表中的位置排序）
        int sortOrder = POPULAR_LANGUAGE_CODES.indexOf(languageCode);
        vo.setSortOrder(sortOrder >= 0 ? sortOrder + 1 : 999);
        
        return vo;
    }
}
