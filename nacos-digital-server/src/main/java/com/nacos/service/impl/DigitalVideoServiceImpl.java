package com.nacos.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nacos.config.TencentCloudConfig;
import com.nacos.entity.bo.TaskStatusUpdateBO;
import com.nacos.entity.dto.DigitalTaskItem;
import com.nacos.entity.dto.DigitalVideoGenerationDTO;
import com.nacos.entity.enums.DigitalNotificationEnum;
import com.nacos.entity.enums.VideoTaskStatusEnum;
import com.nacos.entity.po.DigitalUserAvatarPO;
import com.nacos.entity.po.DigitalSystemAvatarPO;
import com.nacos.entity.po.DigitalVideoTaskItemPO;
import com.nacos.entity.po.DigitalVideoTaskPO;
import com.nacos.enums.DDUseRuleEnum;
import com.nacos.mapper.DigitalUserAvatarMapper;
import com.nacos.mapper.DigitalSystemAvatarMapper;
import com.nacos.mapper.DigitalVideoTaskItemMapper;
import com.nacos.mapper.DigitalVideoTaskMapper;
import com.nacos.model.TXDigital.TXDigitalApisUtil;
import com.nacos.result.Result;
import com.nacos.service.*;
import com.nacos.utils.MessageSendUtil;
import com.business.message.mq.BRedisServiceUtil;
import com.business.message.BMessageSendEnum;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 数字人视频服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DigitalVideoServiceImpl implements DigitalVideoService {

    private final DigitalUserAvatarMapper digitalUserAvatarMapper;
    private final DigitalSystemAvatarMapper digitalSystemAvatarMapper;
    private final DigitalVideoTaskMapper digitalVideoTaskMapper;
    private final DigitalVideoTaskService digitalVideoTaskService;
    private final DigitalVideoAsyncService digitalVideoAsyncService;
    private final TXDigitalApisUtil txDigitalApisUtil;
    private final DigitalVideoTaskItemMapper digitalVideoTaskItemMapper;
    private final DigitalNotificationService digitalNotificationService;
    @Autowired
    private TencentCloudConfig tencentCloudConfig;
    private final FeiyongService feiyongService;

    /**
     * 提交数字人生成视频请求
     *
     * @param digitalVideoGenerationDTO 提交数字人生成视频请求参数
     * @return 任务ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> submitDigitalVideoGenerationRequest(DigitalVideoGenerationDTO digitalVideoGenerationDTO) {
        String methodName = "submitDigitalVideoGenerationRequest";
        log.info("[{}] 开始提交数字人生成视频任务", methodName);

        try {
            // 1. 参数校验
            if (digitalVideoGenerationDTO == null || digitalVideoGenerationDTO.getTaskItems() == null || digitalVideoGenerationDTO.getTaskItems().isEmpty()) {
                log.error("[{}] 任务列表为空", methodName);
                return Result.ERROR("任务列表不能为空");
            }
            //判断taskItems里面voiceFile和text不能同时为空
            // 校验每个子任务的 voiceFile 和 text 不能同时为空
            for (DigitalTaskItem item : digitalVideoGenerationDTO.getTaskItems()) {
                boolean isVoiceFileEmpty = item.getVoiceFile() == null || item.getVoiceFile().isEmpty() || StringUtils.isEmpty(item.getVoiceUrl()) ;
                boolean isTextEmpty = StringUtils.isBlank(item.getText());
                if (isVoiceFileEmpty && isTextEmpty) {
                    Result.ERROR("语音文件和文本不能同时为空");
                }
            }

            // 检查余额
            if (!feiyongService.checkYue(Long.valueOf(digitalVideoGenerationDTO.getUserId()), DDUseRuleEnum.VIDEO_CREATE_PER_MIN.getRedisKey())){
                return Result.ERROR("余额不足");
            }

            // 2. 查询并验证数字人信息
            Map<String, String> avatarVideoUrlMap = new HashMap<>();
            for (DigitalTaskItem item : digitalVideoGenerationDTO.getTaskItems()) {
                if (StringUtils.isBlank(item.getAvatarId())) {
                    log.error("[{}] 数字人ID为空", methodName);
                    return Result.ERROR("数字人ID不能为空");
                }

                String avatarId = item.getAvatarId();
                log.info("[{}] 开始查询数字人信息，avatarId：{}", methodName, avatarId);

                // 2.1 查询用户数字人表
                DigitalUserAvatarPO userAvatar = digitalUserAvatarMapper.selectOne(
                        new LambdaQueryWrapper<DigitalUserAvatarPO>()
                                .eq(DigitalUserAvatarPO::getAvatarId, avatarId)
                                .eq(DigitalUserAvatarPO::getIsDeleted, 0)
                );

                // 2.2 查询系统数字人表
                DigitalSystemAvatarPO systemAvatar = digitalSystemAvatarMapper.selectOne(
                        new LambdaQueryWrapper<DigitalSystemAvatarPO>()
                                .eq(DigitalSystemAvatarPO::getAvatarId, avatarId)
                                .eq(DigitalSystemAvatarPO::getIsDeleted, 0)
                );

                // 2.3 处理查询结果
                String source = null;
                String avatarVideoUrl = null;

                if (userAvatar != null) {
                    // 优先使用用户数字人
                    source = "用户数字人";
                    avatarVideoUrl = userAvatar.getAvatarVideoUrl();
                } else if (systemAvatar != null) {
                    source = "系统数字人";
                    avatarVideoUrl = systemAvatar.getAvatarVideoUrl();
                } else {
                    log.error("[{}] 数字人不存在（用户表和系统表都未找到），avatarId：{}", methodName, avatarId);
                    return Result.ERROR(String.format("数字人不存在：%s", avatarId));
                }

                if (StringUtils.isBlank(avatarVideoUrl)) {
                    log.error("[{}] {}训练视频URL为空，avatarId：{}", methodName, source, avatarId);
                    return Result.ERROR(String.format("数字人训练视频URL为空：%s", avatarId));
                }

                log.info("[{}] 成功获取{}信息，avatarId：{}", methodName, source, avatarId);
                // 保存数字人ID与视频URL的映射
                avatarVideoUrlMap.put(avatarId, avatarVideoUrl);
            }

            // 3. 创建主任务记录（状态为排队中）
            Result<String> createResult = digitalVideoTaskService.createTask(
                    digitalVideoGenerationDTO.getUserId(),
                    digitalVideoGenerationDTO.getTaskItems(),
                    avatarVideoUrlMap
            );

            if (!createResult.isSuccess()) {
                log.error("[{}] 创建任务记录失败：{}", methodName, createResult.getMessage());
                return Result.ERROR(createResult.getMessage());
            }

            String generateTaskId = createResult.getData();
            log.info("[{}] 任务已创建并进入队列，等待处理，generateTaskId：{}", methodName, generateTaskId);

            // 推送任务状态更新
            pushTaskStatus(digitalVideoGenerationDTO.getUserId(), generateTaskId,
                    VideoTaskStatusEnum.QUEUING.getValue(), "任务已进入队列，等待处理");

            return Result.SUCCESS("任务已提交，正在排队处理", generateTaskId);

        } catch (Exception e) {
            log.error("[{}] 提交数字人生成视频异常：{}", methodName, e.getMessage(), e);
            return Result.ERROR("提交数字人生成视频失败：" + e.getMessage());
        }
    }

    /**
     * 处理排队中的任务
     */
    @Override
    public void processQueueingTasks() {
        String methodName = "processQueueingTasks";
        try {
            // 查询任务队列中有几个排队任务
            long queueingCount = digitalVideoTaskMapper.selectCount(
                new LambdaQueryWrapper<DigitalVideoTaskPO>()
                    .eq(DigitalVideoTaskPO::getStatus, VideoTaskStatusEnum.QUEUING.getValue())
                    .eq(DigitalVideoTaskPO::getIsDeleted, 0)
            );
            if (queueingCount == 0) {
                // log.debug("[{}] 没有排队中的任务", methodName);
                return;
            }

            log.info("[{}] 当前任务队列中排队任务数量：{}", methodName, queueingCount);
            // 1. 首先检查是否有进行中的任务
            long inProgressCount = digitalVideoTaskMapper.selectCount(
                    new LambdaQueryWrapper<DigitalVideoTaskPO>()
                            .eq(DigitalVideoTaskPO::getStatus, VideoTaskStatusEnum.IN_PROGRESS.getValue())
                            .eq(DigitalVideoTaskPO::getIsDeleted, 0)
            );

            // 如果已经有进行中的任务，则不处理排队任务
            if (inProgressCount > 0) {
                log.info("[{}] 当前有{}个进行中的任务，暂不处理排队任务", methodName, inProgressCount);
                return;
            }

            // 2. 查询排队中的任务（按创建时间升序，最早创建的任务优先处理）
            List<DigitalVideoTaskPO> queueingTasks = digitalVideoTaskMapper.selectList(
                    new LambdaQueryWrapper<DigitalVideoTaskPO>()
                            .eq(DigitalVideoTaskPO::getStatus, VideoTaskStatusEnum.QUEUING.getValue())
                            .eq(DigitalVideoTaskPO::getIsDeleted, 0)
                            .orderByAsc(DigitalVideoTaskPO::getCreatedTime)
                            .last("LIMIT 1") // 只取一个任务处理
            );

            if (queueingTasks.isEmpty()) {
                // log.debug("[{}] 没有排队中的任务", methodName);
                return;
            }

            // 3. 处理队列中的第一个任务
            DigitalVideoTaskPO task = queueingTasks.getFirst();
            log.info("[{}] 开始处理排队任务：taskId={}, userId={}", methodName, task.getTaskId(), task.getUserId());

            try {
                // 4. 查询子任务详情
                List<DigitalVideoTaskItemPO> taskItems = digitalVideoTaskItemMapper.selectList(
                        new LambdaQueryWrapper<DigitalVideoTaskItemPO>()
                                .eq(DigitalVideoTaskItemPO::getTaskId, task.getTaskId())
                                .eq(DigitalVideoTaskItemPO::getIsDeleted, 0)
                                .orderByAsc(DigitalVideoTaskItemPO::getSequence)
                );

                if (taskItems.isEmpty()) {
                    String errorMsg = "该任务没有子任务";
                    log.error("[{}] {}：taskId={}", methodName, errorMsg, task.getTaskId());
                    digitalVideoTaskService.updateTask(TaskStatusUpdateBO.builder()
                        .taskId(task.getTaskId())
                        .status(VideoTaskStatusEnum.FAILED.getValue())
                        .errorMsg(errorMsg)
                        .build()
                    );
                    pushTaskStatus(task.getUserId(), task.getTaskId(), VideoTaskStatusEnum.FAILED.getValue(), errorMsg);
                    return;
                }
                // 更新任务状态为进行中
                digitalVideoTaskService.updateTask(TaskStatusUpdateBO.builder()
                    .taskId(task.getTaskId())
                    .status(VideoTaskStatusEnum.IN_PROGRESS.getValue())
                    .build()
                );
                // 提交到异步服务处理
                digitalVideoAsyncService.processVideoTasks(
                        task.getTaskId(),
                        task.getUserId(),
                        taskItems
                );
                log.info("[{}] 任务已提交到异步服务处理：taskId={}", methodName, task.getTaskId());
                pushTaskStatus(task.getUserId(), task.getTaskId(), VideoTaskStatusEnum.IN_PROGRESS.getValue(), "任务已提交到异步服务处理");
            } catch (Exception e) {
                String errorMsg = "处理任务异常：" + e.getMessage();
                log.error("[{}] {}：taskId={}", methodName, errorMsg, task.getTaskId(), e);
                digitalVideoTaskService.updateTask(TaskStatusUpdateBO.builder()
                    .taskId(task.getTaskId())
                    .status(VideoTaskStatusEnum.FAILED.getValue())
                    .errorMsg(errorMsg)
                    .build()
                );
                pushTaskStatus(task.getUserId(), task.getTaskId(), VideoTaskStatusEnum.FAILED.getValue(), errorMsg);
            }
        } catch (Exception e) {
            log.error("[{}] 处理排队中的任务异常", methodName, e);
        }
    }

    /**
     * 处理超时任务
     */
    @Override
    public void processTimeoutTasks() {
        String methodName = "processTimeoutTasks";
        log.info("[{}] 开始处理超时任务", methodName);

        try {
            // 获取配置的超时时间（分钟）
            int timeoutMinutes = 30; // 默认30分钟

            // 计算超时时间点
            LocalDateTime timeoutThreshold = LocalDateTime.now().minusMinutes(timeoutMinutes);

            // 查询可能超时的任务（进行中状态且更新时间超过阈值）
            List<DigitalVideoTaskPO> potentialTimeoutTasks = digitalVideoTaskMapper.selectList(
                    new LambdaQueryWrapper<DigitalVideoTaskPO>()
                            .eq(DigitalVideoTaskPO::getStatus, VideoTaskStatusEnum.IN_PROGRESS.getValue())
                            .eq(DigitalVideoTaskPO::getIsDeleted, 0)
                            .lt(DigitalVideoTaskPO::getUpdateTime, timeoutThreshold)
            );

            if (potentialTimeoutTasks.isEmpty()) {
                log.info("[{}] 没有超时的任务", methodName);
                return;
            }

            log.info("[{}] 发现{}个可能超时的任务", methodName, potentialTimeoutTasks.size());

            // 处理每个可能超时的任务
            for (DigitalVideoTaskPO task : potentialTimeoutTasks) {
                try {
                    log.info("[{}] 处理超时任务：taskId={}, createdTime={}, updateTime={}",
                            methodName, task.getTaskId(), task.getCreatedTime(), task.getUpdateTime());

                    // 更新任务状态为超时
                    task.setStatus(VideoTaskStatusEnum.TIMEOUT.getValue());
                    task.setErrorMsg("任务处理超时，请重试");
                    task.setUpdateTime(new Date());
                    digitalVideoTaskMapper.updateById(task);

                    // 推送任务状态更新
                    pushTaskStatus(task.getUserId(), task.getTaskId(), VideoTaskStatusEnum.TIMEOUT.getValue(), "任务处理超时，请重试");

                    log.info("[{}] 任务{}已标记为超时", methodName, task.getTaskId());

                } catch (Exception e) {
                    log.error("[{}] 处理超时任务{}异常", methodName, task.getTaskId(), e);
                }
            }
        } catch (Exception e) {
            log.error("[{}] 处理超时任务异常", methodName, e);
        }
    }

    /**
     * 推送任务状态更新
     * @param userId 用户ID
     * @param taskId 任务ID
     * @param status 任务状态
     * @param message 状态消息
     */
    private void pushTaskStatus(String userId, String taskId, Integer status, String message) {
        String methodName = "pushTaskStatus";
        log.info("[{}] 开始推送任务状态：userId={}, taskId={}, status={}", methodName, userId, taskId, status);
        
        try {
            // 1. 查询任务信息
            DigitalVideoTaskPO task = digitalVideoTaskMapper.selectOne(
                new LambdaQueryWrapper<DigitalVideoTaskPO>()
                    .eq(DigitalVideoTaskPO::getTaskId, taskId)
                    .eq(DigitalVideoTaskPO::getIsDeleted, 0)
            );
            
            if (task != null) {
                // 2. 如果任务完成或失败，发送通知消息
                if (status != null && (status == VideoTaskStatusEnum.SUCCESS.getValue() || status == VideoTaskStatusEnum.FAILED.getValue())) {
                    log.debug("[{}] 构建通知消息：userId={}, taskId={}, status={}", methodName, userId, taskId, status);
                    
                    // 使用新的数字人专用通知服务
                    int notifType = status == VideoTaskStatusEnum.SUCCESS.getValue() ? 
                        DigitalNotificationEnum.VIDEO_TASK_SUCCESS.getValue() : 
                        DigitalNotificationEnum.VIDEO_TASK_FAIL.getValue();
                    
                    String notifContent = message != null ? message : 
                        (status == VideoTaskStatusEnum.SUCCESS.getValue() ? 
                            "您的数字人视频已生成完成，点击查看" : 
                            "很抱歉，您的数字人视频生成失败，请重试");
                    
                    // 1. 创建通知记录
                    digitalNotificationService.createNotification(
                        Long.valueOf(userId),
                        taskId,
                        notifType,
                        notifContent
                    );
                    
                    // 2. 发送通知消息
                    Map<String, Object> dataMap = new HashMap<>();
                    dataMap.put("taskId", taskId);
                    dataMap.put("notifType", notifType);
                    dataMap.put("notifTitle", DigitalNotificationEnum.getDesc(notifType));
                    dataMap.put("notifContent", notifContent);
                    
                    String jsonMessage = MessageSendUtil.getJSONStr(
                        userId,
                        BMessageSendEnum.VIDEO_JOB_DIGITAL_PUSH,
                        dataMap
                    );
                    BRedisServiceUtil.sendMessageDigital(jsonMessage);
                }
            } else {
                log.warn("[{}] 未找到任务信息，无法推送状态: userId={}, taskId={}", methodName, userId, taskId);
            }
        } catch (Exception e) {
            log.error("[{}] 推送任务状态失败：userId={}, taskId={}, error={}", methodName, userId, taskId, e.getMessage(), e);
        }
    }
}
