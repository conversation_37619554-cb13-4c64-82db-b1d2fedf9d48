package com.nacos.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nacos.entity.bo.SyncResult;
import com.nacos.entity.enums.SyncStatus;
import com.nacos.entity.po.ProviderCapabilityPO;
import com.nacos.mapper.ProviderCapabilityMapper;
import com.nacos.mapper.SysLanguageMapper;
import com.nacos.mapper.SysProviderMapper;
import com.nacos.model.SoundView.model.response.LanguageListResponseBO;
import com.nacos.result.Result;
import com.nacos.service.LanguageSupportSyncService;
import com.nacos.service.adapter.LingyangLanguageAdapter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 语种支持同步服务实现类
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LanguageSupportSyncServiceImpl extends ServiceImpl<ProviderCapabilityMapper, ProviderCapabilityPO> 
    implements LanguageSupportSyncService {

    private final SysLanguageMapper sysLanguageMapper;
    private final SysProviderMapper sysProviderMapper;
    private final LingyangLanguageAdapter lingyangLanguageAdapter;

    /**
     * 同步统计信息内部类
     */
    private static class SyncStatistics {
        private int totalCount = 0;
        private int newCount = 0;
        private int updatedCount = 0;
        private int skippedCount = 0;
        private int errorCount = 0;

        public void incrementTotal() { totalCount++; }
        public void incrementNew() { newCount++; }
        public void incrementUpdated() { updatedCount++; }
        public void incrementSkipped() { skippedCount++; }
        public void incrementError() { errorCount++; }

        public int getTotalCount() { return totalCount; }
        public int getNewCount() { return newCount; }
        public int getUpdatedCount() { return updatedCount; }
        public int getSkippedCount() { return skippedCount; }
        public int getErrorCount() { return errorCount; }

        @Override
        public String toString() {
            return String.format("总计:%d, 新增:%d, 更新:%d, 跳过:%d, 错误:%d",
                               totalCount, newCount, updatedCount, skippedCount, errorCount);
        }
    }
    
    @Override
    public Result<String> syncAllLanguageSupport() {
        String methodName = "syncAllLanguageSupport";
        log.info("[{}] 开始同步所有服务商语种支持数据", methodName);
        
        try {
            List<String> results = new ArrayList<>();
            
            // 同步LINGYANG服务商
            Result<String> lingyangResult = syncLingyangLanguageSupport();
            results.add("LINGYANG: " + lingyangResult.getMessage());
            
            // TODO: 后续可以添加其他服务商的同步
            // Result<String> azureResult = syncAzureLanguageSupport();
            // results.add("AZURE: " + azureResult.getMessage());
            
            String message = String.join("; ", results);
            log.info("[{}] 所有服务商语种支持数据同步完成: {}", methodName, message);
            return Result.SUCCESS(message);
            
        } catch (Exception e) {
            log.error("[{}] 同步所有服务商语种支持数据失败", methodName, e);
            return Result.ERROR("同步失败: " + e.getMessage());
        }
    }

    @Override
    public Result<String> syncLanguageSupportByProvider(String providerCode) {
        if (!StringUtils.hasText(providerCode)) {
            return Result.ERROR("服务商代码不能为空");
        }
        
        switch (providerCode.toUpperCase()) {
            case "LINGYANG":
                return syncLingyangLanguageSupport();
            // TODO: 后续添加其他服务商
            // case "AZURE":
            //     return syncAzureLanguageSupport();
            default:
                return Result.ERROR("不支持的服务商: " + providerCode);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> syncLingyangLanguageSupport() {
        String methodName = "syncLingyangLanguageSupport";
        log.info("[{}] 开始同步LINGYANG语种支持数据", methodName);

        try {
            // 1. 调用LingyangLanguageAdapter获取语种数据
            log.info("[{}] 步骤1: 获取LINGYANG语种列表", methodName);
            Result<List<LanguageListResponseBO.LanguageInfo>> languageResult =
                lingyangLanguageAdapter.fetchSupportedLanguages();

            if (!languageResult.isSuccess() || languageResult.getData() == null) {
                log.error("[{}] 获取LINGYANG语种列表失败: {}", methodName, languageResult.getMessage());
                return Result.ERROR("获取语种列表失败: " + languageResult.getMessage());
            }

            List<LanguageListResponseBO.LanguageInfo> languages = languageResult.getData();
            log.info("[{}] 成功获取{}种支持的语言", methodName, languages.size());

            // 2. 数据转换和验证
            log.info("[{}] 步骤2: 转换语种数据为ProviderCapabilityPO格式", methodName);
            List<ProviderCapabilityPO> capabilities = lingyangLanguageAdapter.convertToProviderCapability(languages);

            if (capabilities.isEmpty()) {
                log.warn("[{}] 转换后的语种配置为空", methodName);
                return Result.SUCCESS("无可用的语种配置需要同步");
            }

            // 验证转换结果
            Result<String> validationResult = lingyangLanguageAdapter.validateConversionResult(capabilities);
            if (!validationResult.isSuccess()) {
                log.error("[{}] 转换结果验证失败: {}", methodName, validationResult.getMessage());
                return Result.ERROR("数据转换验证失败: " + validationResult.getMessage());
            }

            log.info("[{}] 转换完成，生成{}个语种配置", methodName, capabilities.size());

            // 3. 数据去重和批量插入数据库
            log.info("[{}] 步骤3: 执行数据同步", methodName);
            SyncStatistics statistics = performDataSync(capabilities, methodName);

            // 4. 生成同步结果报告
            String resultMessage = generateSyncResultMessage(statistics, languages, capabilities);
            log.info("[{}] LINGYANG语种支持数据同步完成: {}", methodName, resultMessage);

            return Result.SUCCESS(resultMessage);

        } catch (Exception e) {
            log.error("[{}] 同步LINGYANG语种支持数据失败", methodName, e);
            return Result.ERROR("同步失败: " + e.getMessage());
        }
    }

    @Override
    public Result<String> retryFailedSyncs() {
        String methodName = "retryFailedSyncs";
        log.info("[{}] 开始重试失败的同步记录", methodName);
        
        try {
            // TODO: 实现重试逻辑
            log.info("[{}] 重试失败的同步记录完成", methodName);
            return Result.SUCCESS("重试完成");
            
        } catch (Exception e) {
            log.error("[{}] 重试失败的同步记录失败", methodName, e);
            return Result.ERROR("重试失败: " + e.getMessage());
        }
    }

    @Override
    public Result<String> retryFailedSyncsByProvider(String providerCode) {
        String methodName = "retryFailedSyncsByProvider";
        log.info("[{}] 开始重试服务商[{}]的失败同步记录", methodName, providerCode);
        
        try {
            // TODO: 实现按服务商重试逻辑
            log.info("[{}] 重试服务商[{}]的失败同步记录完成", methodName, providerCode);
            return Result.SUCCESS("重试完成");
            
        } catch (Exception e) {
            log.error("[{}] 重试服务商[{}]的失败同步记录失败", methodName, providerCode, e);
            return Result.ERROR("重试失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getSyncStatusStatistics() {
        String methodName = "getSyncStatusStatistics";
        log.info("[{}] 获取同步状态统计", methodName);
        
        try {
            Map<String, Object> statistics = new HashMap<>();
            
            // 统计总的语种对数量
            long totalCount = count();
            statistics.put("totalCount", totalCount);
            
            // 按服务商统计
            List<ProviderCapabilityPO> allRecords = list();
            Map<String, Long> providerStats = allRecords.stream()
                .collect(Collectors.groupingBy(
                    ProviderCapabilityPO::getProviderCode,
                    Collectors.counting()
                ));
            statistics.put("providerStats", providerStats);
            
            // 按启用状态统计
            long enabledCount = count(new LambdaQueryWrapper<ProviderCapabilityPO>()
                .eq(ProviderCapabilityPO::getEnabled, 1));
            statistics.put("enabledCount", enabledCount);
            statistics.put("disabledCount", totalCount - enabledCount);
            
            log.info("[{}] 同步状态统计获取完成: {}", methodName, statistics);
            return statistics;
            
        } catch (Exception e) {
            log.error("[{}] 获取同步状态统计失败", methodName, e);
            return new HashMap<>();
        }
    }

    @Override
    public Map<String, Object> getSyncStatusStatisticsByProvider(String providerCode) {
        String methodName = "getSyncStatusStatisticsByProvider";
        log.info("[{}] 获取服务商[{}]的同步状态统计", methodName, providerCode);
        
        try {
            Map<String, Object> statistics = new HashMap<>();
            
            LambdaQueryWrapper<ProviderCapabilityPO> wrapper = new LambdaQueryWrapper<ProviderCapabilityPO>()
                .eq(ProviderCapabilityPO::getProviderCode, providerCode);
            
            long totalCount = count(wrapper);
            statistics.put("totalCount", totalCount);
            statistics.put("providerCode", providerCode);
            
            long enabledCount = count(wrapper.clone().eq(ProviderCapabilityPO::getEnabled, 1));
            statistics.put("enabledCount", enabledCount);
            statistics.put("disabledCount", totalCount - enabledCount);
            
            log.info("[{}] 服务商[{}]的同步状态统计获取完成: {}", methodName, providerCode, statistics);
            return statistics;
            
        } catch (Exception e) {
            log.error("[{}] 获取服务商[{}]的同步状态统计失败", methodName, providerCode, e);
            return new HashMap<>();
        }
    }

    @Override
    public List<ProviderCapabilityPO> getSupportedLanguagePairs(String providerCode) {
        String methodName = "getSupportedLanguagePairs";
        log.info("[{}] 获取支持的语种对列表, 服务商: {}", methodName, providerCode);
        
        try {
            LambdaQueryWrapper<ProviderCapabilityPO> wrapper = new LambdaQueryWrapper<ProviderCapabilityPO>()
                .eq(ProviderCapabilityPO::getEnabled, 1);
            
            if (StringUtils.hasText(providerCode)) {
                wrapper.eq(ProviderCapabilityPO::getProviderCode, providerCode);
            }
            
            List<ProviderCapabilityPO> result = list(wrapper);
            log.info("[{}] 获取到{}个支持的语种对", methodName, result.size());
            return result;
            
        } catch (Exception e) {
            log.error("[{}] 获取支持的语种对列表失败", methodName, e);
            return new ArrayList<>();
        }
    }

    @Override
    public boolean isLanguagePairSupported(String providerCode, String sourceLanguage, String targetLanguage) {
        String methodName = "isLanguagePairSupported";
        log.debug("[{}] 检查语种对是否被支持: {} {} -> {}", methodName, providerCode, sourceLanguage, targetLanguage);

        try {
            // 检查服务商是否同时支持源语言和目标语言
            long sourceCount = count(new LambdaQueryWrapper<ProviderCapabilityPO>()
                .eq(ProviderCapabilityPO::getProviderCode, providerCode)
                .eq(ProviderCapabilityPO::getLanguageCode, sourceLanguage)
                .eq(ProviderCapabilityPO::getEnabled, 1));

            long targetCount = count(new LambdaQueryWrapper<ProviderCapabilityPO>()
                .eq(ProviderCapabilityPO::getProviderCode, providerCode)
                .eq(ProviderCapabilityPO::getLanguageCode, targetLanguage)
                .eq(ProviderCapabilityPO::getEnabled, 1));

            boolean supported = sourceCount > 0 && targetCount > 0;
            log.debug("[{}] 语种对支持检查结果: {}", methodName, supported);
            return supported;
            
        } catch (Exception e) {
            log.error("[{}] 检查语种对支持失败", methodName, e);
            return false;
        }
    }

    @Override
    public ProviderCapabilityPO getProviderLanguageMapping(String providerCode, String languageCode) {
        String methodName = "getProviderLanguageMapping";
        log.debug("[{}] 获取服务商语言代码映射: {} {}", methodName, providerCode, languageCode);

        try {
            ProviderCapabilityPO result = getOne(new LambdaQueryWrapper<ProviderCapabilityPO>()
                .eq(ProviderCapabilityPO::getProviderCode, providerCode)
                .eq(ProviderCapabilityPO::getLanguageCode, languageCode)
                .eq(ProviderCapabilityPO::getEnabled, 1));

            log.debug("[{}] 语言代码映射获取结果: {}", methodName, result != null ? "找到" : "未找到");
            return result;
            
        } catch (Exception e) {
            log.error("[{}] 获取服务商语言代码映射失败", methodName, e);
            return null;
        }
    }

    @Override
    public Result<String> cleanExpiredFailedRecords(Integer days) {
        String methodName = "cleanExpiredFailedRecords";
        log.info("[{}] 开始清理{}天前的失败记录", methodName, days);
        
        try {
            // TODO: 实现清理逻辑
            log.info("[{}] 清理过期失败记录完成", methodName);
            return Result.SUCCESS("清理完成");
            
        } catch (Exception e) {
            log.error("[{}] 清理过期失败记录失败", methodName, e);
            return Result.ERROR("清理失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> checkDataConsistency() {
        String methodName = "checkDataConsistency";
        log.info("[{}] 开始检查数据一致性", methodName);
        
        try {
            Map<String, Object> result = new HashMap<>();
            
            // TODO: 实现数据一致性检查逻辑
            result.put("consistent", true);
            result.put("checkTime", LocalDateTime.now());
            
            log.info("[{}] 数据一致性检查完成", methodName);
            return result;
            
        } catch (Exception e) {
            log.error("[{}] 数据一致性检查失败", methodName, e);
            return new HashMap<>();
        }
    }

    @Override
    public Result<String> fixDataInconsistency() {
        String methodName = "fixDataInconsistency";
        log.info("[{}] 开始修复数据不一致问题", methodName);
        
        try {
            // TODO: 实现数据修复逻辑
            log.info("[{}] 数据不一致问题修复完成", methodName);
            return Result.SUCCESS("修复完成");
            
        } catch (Exception e) {
            log.error("[{}] 修复数据不一致问题失败", methodName, e);
            return Result.ERROR("修复失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getRecentSyncLogs(Integer limit) {
        String methodName = "getRecentSyncLogs";
        log.info("[{}] 获取最近{}条同步日志", methodName, limit);
        
        try {
            // TODO: 实现同步日志获取逻辑
            List<Map<String, Object>> logs = new ArrayList<>();
            
            log.info("[{}] 获取最近同步日志完成", methodName);
            return logs;
            
        } catch (Exception e) {
            log.error("[{}] 获取最近同步日志失败", methodName, e);
            return new ArrayList<>();
        }
    }

    @Override
    public Result<String> refreshLanguageSupportCache() {
        String methodName = "refreshLanguageSupportCache";
        log.info("[{}] 开始刷新语种支持缓存", methodName);
        
        try {
            // TODO: 实现缓存刷新逻辑
            log.info("[{}] 语种支持缓存刷新完成", methodName);
            return Result.SUCCESS("缓存刷新完成");
            
        } catch (Exception e) {
            log.error("[{}] 刷新语种支持缓存失败", methodName, e);
            return Result.ERROR("缓存刷新失败: " + e.getMessage());
        }
    }

    /**
     * 执行数据同步
     *
     * @param capabilities 语种配置列表
     * @param methodName 方法名（用于日志）
     * @return 同步统计信息
     */
    private SyncStatistics performDataSync(List<ProviderCapabilityPO> capabilities, String methodName) {
        SyncStatistics statistics = new SyncStatistics();

        log.info("[{}] 开始执行数据同步，待处理记录数: {}", methodName, capabilities.size());

        // 查询现有的LINGYANG语种配置
        List<ProviderCapabilityPO> existingCapabilities = list(
            new LambdaQueryWrapper<ProviderCapabilityPO>()
                .eq(ProviderCapabilityPO::getProviderCode, "LINGYANG")
        );

        // 构建现有配置的映射表，用于快速查找
        Map<String, ProviderCapabilityPO> existingMap = existingCapabilities.stream()
            .collect(Collectors.toMap(
                capability -> capability.getLanguageCode(),
                capability -> capability
            ));

        log.info("[{}] 现有LINGYANG语种配置数量: {}", methodName, existingCapabilities.size());

        // 分批处理数据
        List<ProviderCapabilityPO> toInsert = new ArrayList<>();
        List<ProviderCapabilityPO> toUpdate = new ArrayList<>();

        for (ProviderCapabilityPO capability : capabilities) {
            statistics.incrementTotal();

            String key = capability.getLanguageCode();
            ProviderCapabilityPO existing = existingMap.get(key);

            if (existing == null) {
                // 新增记录
                toInsert.add(capability);
                statistics.incrementNew();
            } else {
                // 检查是否需要更新
                if (needsUpdate(existing, capability)) {
                    capability.setId(existing.getId());
                    capability.setCreatedTime(existing.getCreatedTime());
                    toUpdate.add(capability);
                    statistics.incrementUpdated();
                } else {
                    statistics.incrementSkipped();
                }
            }
        }

        // 批量插入新记录
        if (!toInsert.isEmpty()) {
            log.info("[{}] 批量插入新记录: {}条", methodName, toInsert.size());
            boolean insertSuccess = saveBatch(toInsert, 1000);
            if (!insertSuccess) {
                log.error("[{}] 批量插入失败", methodName);
                statistics.errorCount += toInsert.size();
            }
        }

        // 批量更新记录
        if (!toUpdate.isEmpty()) {
            log.info("[{}] 批量更新记录: {}条", methodName, toUpdate.size());
            boolean updateSuccess = updateBatchById(toUpdate, 1000);
            if (!updateSuccess) {
                log.error("[{}] 批量更新失败", methodName);
                statistics.errorCount += toUpdate.size();
            }
        }

        log.info("[{}] 数据同步完成: {}", methodName, statistics);
        return statistics;
    }

    /**
     * 构建语言对的唯一键
     */
    private String buildLanguagePairKey(String sourceLanguage, String targetLanguage) {
        return sourceLanguage + "->" + targetLanguage;
    }

    /**
     * 检查是否需要更新
     */
    private boolean needsUpdate(ProviderCapabilityPO existing, ProviderCapabilityPO newCapability) {
        // 检查关键字段是否有变化
        return !Objects.equals(existing.getProviderLanguageCode(), newCapability.getProviderLanguageCode()) ||
               !Objects.equals(existing.getEnabled(), newCapability.getEnabled());
    }

    /**
     * 生成同步结果消息
     */
    private String generateSyncResultMessage(SyncStatistics statistics,
            List<LanguageListResponseBO.LanguageInfo> languages,
            List<ProviderCapabilityPO> capabilities) {

        String conversionStats = lingyangLanguageAdapter.getConversionStatistics(languages, capabilities);
        return String.format("LINGYANG语种同步完成 - %s, 数据处理: %s",
                            conversionStats, statistics.toString());
    }

    // ==================== SyncResult相关方法实现 ====================

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SyncResult syncLingyangLanguageSupportWithResult() {
        String methodName = "syncLingyangLanguageSupportWithResult";
        LocalDateTime startTime = LocalDateTime.now();

        log.info("[{}] 开始同步LINGYANG语种支持数据（返回详细结果）", methodName);

        // 创建进行中的同步结果
        SyncResult syncResult = SyncResult.inProgress("LINGYANG");
        syncResult.setStartTime(startTime);

        try {
            // 1. 获取语种数据
            Result<List<LanguageListResponseBO.LanguageInfo>> languageResult =
                lingyangLanguageAdapter.fetchSupportedLanguages();

            if (!languageResult.isSuccess() || languageResult.getData() == null) {
                String errorMsg = "获取语种列表失败: " + languageResult.getMessage();
                log.error("[{}] {}", methodName, errorMsg);
                return syncResult.toBuilder()
                    .success(false)
                    .status(SyncStatus.FAILED)
                    .message("同步失败")
                    .errorMessage(errorMsg)
                    .endTime(LocalDateTime.now())
                    .build()
                    .withTiming(startTime, LocalDateTime.now());
            }

            List<LanguageListResponseBO.LanguageInfo> languages = languageResult.getData();

            // 2. 数据转换
            List<ProviderCapabilityPO> capabilities = lingyangLanguageAdapter.convertToProviderCapability(languages);

            if (capabilities.isEmpty()) {
                log.warn("[{}] 转换后的语种配置为空", methodName);
                return syncResult.toBuilder()
                    .success(true)
                    .status(SyncStatus.SKIPPED)
                    .message("无可用的语种配置需要同步")
                    .endTime(LocalDateTime.now())
                    .build()
                    .withTiming(startTime, LocalDateTime.now())
                    .withStatistics(0, 0, 0, 0, 0);
            }

            // 3. 执行数据同步
            SyncStatistics statistics = performDataSync(capabilities, methodName);

            // 4. 构建成功结果
            LocalDateTime endTime = LocalDateTime.now();
            String conversionStats = lingyangLanguageAdapter.getConversionStatistics(languages, capabilities);
            String resultMessage = String.format("LINGYANG语种同步完成 - %s", conversionStats);

            return syncResult.toBuilder()
                .success(true)
                .status(statistics.getErrorCount() > 0 ? SyncStatus.PARTIAL_SUCCESS : SyncStatus.SUCCESS)
                .message(resultMessage)
                .statisticsInfo(statistics.toString())
                .endTime(endTime)
                .build()
                .withTiming(startTime, endTime)
                .withStatistics(statistics.getTotalCount(), statistics.getNewCount(),
                              statistics.getUpdatedCount(), statistics.getSkippedCount(),
                              statistics.getErrorCount());

        } catch (Exception e) {
            log.error("[{}] 同步LINGYANG语种支持数据异常", methodName, e);
            return syncResult.toBuilder()
                .success(false)
                .status(SyncStatus.FAILED)
                .message("同步失败")
                .errorMessage(e.getMessage())
                .endTime(LocalDateTime.now())
                .build()
                .withTiming(startTime, LocalDateTime.now())
                .addErrorDetail("异常信息: " + e.getMessage());
        }
    }

    @Override
    public SyncResult syncLanguageSupportByProviderWithResult(String providerCode) {
        String methodName = "syncLanguageSupportByProviderWithResult";
        log.info("[{}] 开始同步服务商[{}]的语种支持数据", methodName, providerCode);

        if (!StringUtils.hasText(providerCode)) {
            return SyncResult.failure(null, "服务商代码不能为空");
        }

        switch (providerCode.toUpperCase()) {
            case "LINGYANG":
                return syncLingyangLanguageSupportWithResult();
            // TODO: 后续添加其他服务商
            // case "AZURE":
            //     return syncAzureLanguageSupportWithResult();
            default:
                return SyncResult.failure(providerCode, "不支持的服务商: " + providerCode);
        }
    }

    @Override
    public List<SyncResult> getRecentSyncResults(String providerCode, Integer limit) {
        String methodName = "getRecentSyncResults";
        log.info("[{}] 获取最近的同步结果，服务商: {}, 限制: {}", methodName, providerCode, limit);

        // TODO: 实现同步历史记录的存储和查询
        // 这里可以考虑：
        // 1. 创建同步历史记录表
        // 2. 在每次同步时保存SyncResult到数据库
        // 3. 提供查询接口

        List<SyncResult> results = new ArrayList<>();
        log.info("[{}] 暂未实现同步历史记录功能", methodName);
        return results;
    }

    @Override
    public Map<String, Object> getSyncResultStatistics(String providerCode) {
        String methodName = "getSyncResultStatistics";
        log.info("[{}] 获取同步结果统计信息，服务商: {}", methodName, providerCode);

        Map<String, Object> statistics = new HashMap<>();

        // 基于当前数据库状态的统计
        Map<String, Object> currentStats = StringUtils.hasText(providerCode)
            ? getSyncStatusStatisticsByProvider(providerCode)
            : getSyncStatusStatistics();

        statistics.putAll(currentStats);

        // TODO: 添加基于同步历史记录的统计
        // statistics.put("totalSyncCount", 0);
        // statistics.put("successRate", 0.0);
        // statistics.put("lastSyncTime", null);

        log.info("[{}] 同步结果统计信息获取完成", methodName);
        return statistics;
    }
}
