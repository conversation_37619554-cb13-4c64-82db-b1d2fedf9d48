package com.nacos.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.business.message.BMessageSendEnum;
import com.business.message.mq.BRedisServiceUtil;
import com.nacos.entity.dto.VideoEditRequestDTO;
import com.nacos.entity.dto.VideoEditTaskItem;
import com.nacos.entity.enums.VideoEditTaskStatusEnum;
import com.nacos.entity.po.VideoEditTaskPO;
import com.nacos.entity.po.VideoEditTaskItemPO;
import com.nacos.enums.DDUseRuleEnum;
import com.nacos.mapper.VideoEditTaskMapper;
import com.nacos.result.Result;
import com.nacos.service.FeiyongService;
import com.nacos.service.VideoEditAsyncService;
import com.nacos.service.VideoEditService;
import com.nacos.service.VideoEditTaskService;
import com.nacos.utils.MessageSendUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 视频编辑Service实现类
 * 参考DigitalVideoServiceImpl的设计模式
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VideoEditServiceImpl implements VideoEditService {

    private final VideoEditTaskService videoEditTaskService;
    private final VideoEditAsyncService videoEditAsyncService;
    private final VideoEditTaskMapper videoEditTaskMapper;
    private final FeiyongService feiyongService;

    // 最大任务数量
    private static final int MAX_TASK_COUNT = 50;

    /**
     * 提交视频编辑请求的主入口方法
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> submitVideoEditRequest(VideoEditRequestDTO videoEditRequestDTO) {
        String methodName = "submitVideoEditRequest";
        log.info("[{}] 开始提交数字人视频编辑请求，userId：{}", methodName, videoEditRequestDTO.getUserId());

        try {
            // 1. 参数校验
            Result<String> validateResult = validateEditRequest(videoEditRequestDTO);
            if (!validateResult.isSuccess()) {
                log.error("[{}] 参数校验失败：{}", methodName, validateResult.getMessage());
                return validateResult;
            }

            // // 2. 检查用户余额-开发环境注释
            // Result<String> balanceResult = checkUserBalance(videoEditRequestDTO.getUserId());
            // if (!balanceResult.isSuccess()) {
            //     log.error("[{}] 余额检查失败：{}", methodName, balanceResult.getMessage());
            //     return balanceResult;
            // }

            // 3. 创建视频编辑任务记录
            Result<String> createResult = createEditTasks(videoEditRequestDTO);
            if (!createResult.isSuccess()) {
                log.error("[{}] 创建任务记录失败：{}", methodName, createResult.getMessage());
                return Result.ERROR(createResult.getMessage());
            }

            // 4. 扣费
            

            String generateTaskId = createResult.getData();
            log.info("[{}] 数字人视频编辑任务已创建并进入队列，等待处理，generateTaskId：{}", methodName, generateTaskId);

            // 推送任务状态更新
            pushTaskStatus(videoEditRequestDTO.getUserId(), generateTaskId,
                    VideoEditTaskStatusEnum.QUEUING.getValue(), "任务已进入队列，等待处理");

            return Result.SUCCESS("任务已提交，正在排队处理", generateTaskId);

        } catch (Exception e) {
            log.error("[{}] 提交数字人视频编辑请求异常：{}", methodName, e.getMessage(), e);
            return Result.ERROR("提交视频编辑请求失败：" + e.getMessage());
        }
    }

    /**
     * 验证编辑请求参数
     */
    private Result<String> validateEditRequest(VideoEditRequestDTO requestDTO) {
        String methodName = "validateEditRequest";
        log.info("[{}] 开始验证编辑请求参数", methodName);

        try {
            // 1. 基础参数校验
            if (requestDTO == null || requestDTO.getTaskItems() == null || requestDTO.getTaskItems().isEmpty()) {
                return Result.ERROR("任务列表不能为空");
            }

            if (StringUtils.isBlank(requestDTO.getUserId())) {
                return Result.ERROR("用户ID不能为空");
            }

            // 2. 任务数量校验
            if (requestDTO.getTaskItems().size() > MAX_TASK_COUNT) {
                return Result.ERROR("任务数量不能超过" + MAX_TASK_COUNT + "个");
            }

            // 3. 校验每个子任务
            for (int i = 0; i < requestDTO.getTaskItems().size(); i++) {
                VideoEditTaskItem item = requestDTO.getTaskItems().get(i);
                Result<String> itemValidateResult = validateTaskItem(item, i + 1);
                if (!itemValidateResult.isSuccess()) {
                    return itemValidateResult;
                }
            }

            log.info("[{}] 编辑请求参数验证通过", methodName);
            return Result.SUCCESS("参数验证通过");

        } catch (Exception e) {
            log.error("[{}] 验证编辑请求参数异常", methodName, e);
            return Result.ERROR("参数验证异常：" + e.getMessage());
        }
    }

    /**
     * 验证单个任务项
     */
    private Result<String> validateTaskItem(VideoEditTaskItem item, int index) {
        String methodName = "validateTaskItem";

        try {
            // 1. 数字人ID校验
            if (StringUtils.isBlank(item.getAvatarId())) {
                return Result.ERROR(String.format("第%d个任务的数字人ID不能为空", index));
            }

            // 2. 音频链接校验
            if (StringUtils.isBlank(item.getVoiceUrl())) {
                return Result.ERROR(String.format("第%d个任务的音频链接不能为空", index));
            }

            // 3. 音频URL格式校验
            String voiceUrl = item.getVoiceUrl();
            if (!voiceUrl.startsWith("http://") && !voiceUrl.startsWith("https://")) {
                return Result.ERROR(String.format("第%d个任务的音频链接格式无效", index));
            }

            // 4. 扩展内容长度校验（可选字段）
            // 注意：other字段现在存储JSON字符串，适当增加长度限制
            if (StringUtils.isNotBlank(item.getOther()) && item.getOther().length() > 50000) {
                return Result.ERROR(String.format("第%d个任务的扩展内容长度不能超过50000字符", index));
            }

            return Result.SUCCESS("任务项验证通过");

        } catch (Exception e) {
            log.error("[{}] 验证任务项异常", methodName, e);
            return Result.ERROR(String.format("第%d个任务验证异常：%s", index, e.getMessage()));
        }
    }

    /**
     * 检查用户余额
     */
    private Result<String> checkUserBalance(String userId) {
        String methodName = "checkUserBalance";
        log.info("[{}] 开始检查用户余额，userId：{}", methodName, userId);

        try {
            // 检查余额
            if (!feiyongService.checkYue(Long.valueOf(userId), DDUseRuleEnum.VIDEO_EDIT_PER_MIN.getRedisKey())) {
                log.warn("[{}] 用户余额不足，userId：{}", methodName, userId);
                return Result.ERROR("余额不足");
            }

            log.info("[{}] 用户余额检查通过，userId：{}", methodName, userId);
            return Result.SUCCESS("余额检查通过");

        } catch (Exception e) {
            log.error("[{}] 检查用户余额异常", methodName, e);
            return Result.ERROR("余额检查异常：" + e.getMessage());
        }
    }



    /**
     * 创建编辑任务记录
     */
    private Result<String> createEditTasks(VideoEditRequestDTO requestDTO) {
        String methodName = "createEditTasks";
        log.info("[{}] 开始创建数字人视频编辑任务记录", methodName);

        try {
            // 获取任务项列表
            List<VideoEditTaskItem> taskItems = requestDTO.getTaskItems();

            // 创建主任务记录（状态为排队中）
            Result<String> createResult = videoEditTaskService.createTask(
                    requestDTO.getUserId(),
                    taskItems,
                    requestDTO.getScreenWidth(),
                    requestDTO.getScreenHeight()
            );

            if (createResult.isSuccess()) {
                log.info("[{}] 数字人视频编辑任务记录创建成功，taskId：{}", methodName, createResult.getData());
                return createResult;
            } else {
                log.error("[{}] 数字人视频编辑任务记录创建失败：{}", methodName, createResult.getMessage());
                return Result.ERROR("创建任务记录失败：" + createResult.getMessage());
            }

        } catch (Exception e) {
            log.error("[{}] 创建数字人视频编辑任务记录异常", methodName, e);
            return Result.ERROR("创建任务记录异常：" + e.getMessage());
        }
    }

    /**
     * 处理排队中的任务
     */
    @Override
    public void processQueueingTasks() {
        String methodName = "processQueueingTasks";
        // log.debug("[{}] 开始处理排队中的视频编辑任务", methodName);

        try {
            // 查询排队中的任务（限制数量避免一次处理过多）
            List<VideoEditTaskPO> queueingTasks = videoEditTaskMapper.selectList(
                new LambdaQueryWrapper<VideoEditTaskPO>()
                    .eq(VideoEditTaskPO::getStatus, VideoEditTaskStatusEnum.QUEUING.getValue())
                    .eq(VideoEditTaskPO::getIsDeleted, 0)
                    .orderByAsc(VideoEditTaskPO::getCreatedTime)
                    .last("LIMIT 10")
            );

            if (queueingTasks.isEmpty()) {
                // log.debug("[{}] 没有排队中的任务", methodName);
                return;
            }

            log.info("[{}] 找到{}个排队中的任务", methodName, queueingTasks.size());

            for (VideoEditTaskPO task : queueingTasks) {
                try {
                    // 更新任务状态为进行中
                    videoEditTaskService.updateTask(com.nacos.entity.bo.VideoEditTaskStatusUpdateBO.builder()
                        .taskId(task.getTaskId())
                        .status(VideoEditTaskStatusEnum.IN_PROGRESS.getValue())
                        .build()
                    );

                    // 获取子任务列表
                    List<VideoEditTaskItemPO> taskItems = videoEditTaskService.getTaskItemsByTaskId(task.getTaskId());

                    // 提交到异步服务处理
                    videoEditAsyncService.processEditTasks(task.getTaskId(), task.getUserId(), taskItems);
                    log.info("[{}] 任务已提交到异步服务处理：taskId={}", methodName, task.getTaskId());
                    pushTaskStatus(task.getUserId(), task.getTaskId(), VideoEditTaskStatusEnum.IN_PROGRESS.getValue(), "任务已提交到异步服务处理");
                } catch (Exception e) {
                    String errorMsg = "处理任务异常：" + e.getMessage();
                    log.error("[{}] {}：taskId={}", methodName, errorMsg, task.getTaskId(), e);
                    videoEditTaskService.updateTask(com.nacos.entity.bo.VideoEditTaskStatusUpdateBO.builder()
                        .taskId(task.getTaskId())
                        .status(VideoEditTaskStatusEnum.FAILED.getValue())
                        .errorMsg(errorMsg)
                        .build()
                    );
                    pushTaskStatus(task.getUserId(), task.getTaskId(), VideoEditTaskStatusEnum.FAILED.getValue(), errorMsg);
                }
            }
        } catch (Exception e) {
            log.error("[{}] 处理排队中的任务异常", methodName, e);
        }
    }

    /**
     * 处理进行中的任务状态检查
     * 用于轮询禅境API状态，确保状态码10（生成中）能继续被检查
     */
    @Override
    public void processInProgressTasks() {
        String methodName = "processInProgressTasks";
        // log.debug("[{}] 开始检查进行中任务状态", methodName);

        try {
            // 查询进行中的子任务（视频生成中状态）
            List<VideoEditTaskItemPO> inProgressItems = videoEditTaskService.getInProgressTaskItems();

            if (inProgressItems.isEmpty()) {
                // log.debug("[{}] 没有进行中的子任务", methodName);
                return;
            }

            log.debug("[{}] 找到{}个进行中的子任务需要检查状态", methodName, inProgressItems.size());

            for (VideoEditTaskItemPO item : inProgressItems) {
                try {
                    if (StringUtils.isNotBlank(item.getApiJobId())) {
                        // 检查任务状态
                        Result<String> checkResult = videoEditAsyncService.checkTaskStatus(
                            item.getApiJobId(), item, item.getUserId());

                        if (checkResult.isSuccess()) {
                            log.debug("[{}] 子任务状态检查完成：subTaskId={}", methodName, item.getSubTaskId());
                        } else {
                            log.debug("[{}] 子任务仍在处理中：subTaskId={}, message={}",
                                    methodName, item.getSubTaskId(), checkResult.getMessage());
                        }
                    }
                } catch (Exception e) {
                    log.error("[{}] 检查子任务状态异常：subTaskId={}", methodName, item.getSubTaskId(), e);
                }
            }
        } catch (Exception e) {
            log.error("[{}] 检查进行中任务状态异常", methodName, e);
        }
    }

    /**
     * 处理超时任务
     */
    @Override
    public void processTimeoutTasks() {
        String methodName = "processTimeoutTasks";
        log.info("[{}] 开始处理超时的视频编辑任务", methodName);

        try {
            // 查询超时的任务（进行中状态超过2小时的任务）
            List<VideoEditTaskPO> timeoutTasks = videoEditTaskMapper.selectList(
                new LambdaQueryWrapper<VideoEditTaskPO>()
                    .eq(VideoEditTaskPO::getStatus, VideoEditTaskStatusEnum.IN_PROGRESS.getValue())
                    .eq(VideoEditTaskPO::getIsDeleted, 0)
                    .lt(VideoEditTaskPO::getUpdateTime, new java.util.Date(System.currentTimeMillis() - 2 * 60 * 60 * 1000))
                    .last("LIMIT 20")
            );

            if (timeoutTasks.isEmpty()) {
                // log.debug("[{}] 没有超时的任务", methodName);
                return;
            }

            log.info("[{}] 找到{}个超时的任务", methodName, timeoutTasks.size());

            for (VideoEditTaskPO task : timeoutTasks) {
                try {
                    // 更新任务状态为超时
                    videoEditTaskService.updateTask(com.nacos.entity.bo.VideoEditTaskStatusUpdateBO.builder()
                        .taskId(task.getTaskId())
                        .status(VideoEditTaskStatusEnum.TIMEOUT.getValue())
                        .errorMsg("任务处理超时")
                        .build()
                    );

                    // 推送超时通知
                    pushTaskStatus(task.getUserId(), task.getTaskId(), VideoEditTaskStatusEnum.TIMEOUT.getValue(), "任务处理超时");
                    log.info("[{}] 任务已标记为超时：taskId={}", methodName, task.getTaskId());
                } catch (Exception e) {
                    log.error("[{}] 处理超时任务异常：taskId={}", methodName, task.getTaskId(), e);
                }
            }
        } catch (Exception e) {
            log.error("[{}] 处理超时任务异常", methodName, e);
        }
    }

    /**
     * 推送任务状态
     */
    private void pushTaskStatus(String userId, String taskId, Integer status, String errorMsg) {
        String methodName = "pushTaskStatus";
        log.info("[{}] 推送任务状态，userId：{}，taskId：{}，status：{}", methodName, userId, taskId, status);

        try {
            // 构建推送数据
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("taskId", taskId);
            dataMap.put("status", status);
            dataMap.put("statusDesc", VideoEditTaskStatusEnum.getDesc(status));
            if (StringUtils.isNotBlank(errorMsg)) {
                dataMap.put("errorMsg", errorMsg);
            }

            // 发送WebSocket消息
            String jsonMessage = MessageSendUtil.getJSONStr(
                userId,
                BMessageSendEnum.VIDEO_EDIT_JOB_PUSH, // 使用专门的视频编辑推送类型
                dataMap
            );
            BRedisServiceUtil.sendMessageDigital(jsonMessage);

            log.info("[{}] 任务状态推送成功，userId：{}，taskId：{}", methodName, userId, taskId);

        } catch (Exception e) {
            log.error("[{}] 推送任务状态失败：userId={}, taskId={}, error={}", methodName, userId, taskId, e.getMessage(), e);
        }
    }
}
