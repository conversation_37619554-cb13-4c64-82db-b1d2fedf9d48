package com.nacos.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nacos.entity.bo.SyncResult;
import com.nacos.entity.po.ProviderCapabilityPO;
import com.nacos.result.Result;

import java.util.List;
import java.util.Map;

/**
 * 语种支持同步服务接口
 * 负责第三方服务商语种数据的同步管理
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
public interface LanguageSupportSyncService extends IService<ProviderCapabilityPO> {
    
    /**
     * 同步所有服务商的语种支持数据
     * @return 同步结果
     */
    Result<String> syncAllLanguageSupport();
    
    /**
     * 同步指定服务商的语种支持数据
     * @param providerCode 服务商代码
     * @return 同步结果
     */
    Result<String> syncLanguageSupportByProvider(String providerCode);
    
    /**
     * 同步LINGYANG服务商的语种支持数据
     * @return 同步结果
     */
    Result<String> syncLingyangLanguageSupport();
    
    /**
     * 重试失败的同步记录
     * @return 重试结果
     */
    Result<String> retryFailedSyncs();
    
    /**
     * 重试指定服务商的失败同步记录
     * @param providerCode 服务商代码
     * @return 重试结果
     */
    Result<String> retryFailedSyncsByProvider(String providerCode);
    
    /**
     * 获取同步状态统计
     * @return 统计信息
     */
    Map<String, Object> getSyncStatusStatistics();
    
    /**
     * 获取同步状态统计（按服务商）
     * @param providerCode 服务商代码
     * @return 统计信息
     */
    Map<String, Object> getSyncStatusStatisticsByProvider(String providerCode);
    
    /**
     * 获取支持的语种对列表
     * @param providerCode 服务商代码（可选）
     * @return 语种对列表
     */
    List<ProviderCapabilityPO> getSupportedLanguagePairs(String providerCode);
    
    /**
     * 检查语种对是否被支持
     * @param providerCode 服务商代码
     * @param sourceLanguage 源语言代码
     * @param targetLanguage 目标语言代码
     * @return 是否支持
     */
    boolean isLanguagePairSupported(String providerCode, String sourceLanguage, String targetLanguage);
    
    /**
     * 获取服务商的语言代码映射
     * @param providerCode 服务商代码
     * @param languageCode 语言代码
     * @return 语言代码映射配置
     */
    ProviderCapabilityPO getProviderLanguageMapping(String providerCode, String languageCode);
    
    /**
     * 清理过期的失败记录
     * @param days 保留天数
     * @return 清理结果
     */
    Result<String> cleanExpiredFailedRecords(Integer days);
    
    /**
     * 检查数据一致性
     * @return 一致性检查结果
     */
    Map<String, Object> checkDataConsistency();
    
    /**
     * 修复数据不一致问题
     * @return 修复结果
     */
    Result<String> fixDataInconsistency();
    
    /**
     * 获取最近的同步日志
     * @param limit 限制数量
     * @return 同步日志列表
     */
    List<Map<String, Object>> getRecentSyncLogs(Integer limit);
    
    /**
     * 强制刷新语种支持缓存
     * @return 刷新结果
     */
    Result<String> refreshLanguageSupportCache();

    // ==================== SyncResult相关方法 ====================

    /**
     * 同步LINGYANG服务商的语种支持数据（返回详细结果）
     * @return 详细同步结果
     */
    SyncResult syncLingyangLanguageSupportWithResult();

    /**
     * 同步指定服务商的语种支持数据（返回详细结果）
     * @param providerCode 服务商代码
     * @return 详细同步结果
     */
    SyncResult syncLanguageSupportByProviderWithResult(String providerCode);

    /**
     * 获取最近的同步结果列表
     * @param providerCode 服务商代码（可选）
     * @param limit 限制数量
     * @return 同步结果列表
     */
    List<SyncResult> getRecentSyncResults(String providerCode, Integer limit);

    /**
     * 获取同步结果统计信息
     * @param providerCode 服务商代码（可选）
     * @return 统计信息
     */
    Map<String, Object> getSyncResultStatistics(String providerCode);
}
