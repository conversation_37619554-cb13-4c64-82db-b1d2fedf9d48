package com.nacos.service;

import com.nacos.entity.vo.LanguageVO;
import com.nacos.result.Result;

import java.util.List;

/**
 * 语种服务接口
 * 提供视频翻译语种支持相关的业务逻辑
 *
 * <AUTHOR>
 * @since 2025-01-05
 */
public interface LanguageService {

    /**
     * 获取支持的翻译语种列表
     * 
     * @param providerCode 服务商代码（可选，用于过滤特定服务商支持的语种）
     * @param popular 是否只返回热门语种（可选）
     * @return 支持的语种列表
     */
    Result<List<LanguageVO>> getSupportedLanguages(String providerCode, Boolean popular);

    /**
     * 获取热门翻译语种列表
     * 
     * @param limit 返回数量限制
     * @return 热门语种列表
     */
    Result<List<LanguageVO>> getPopularLanguages(Integer limit);



    /**
     * 检查语言对是否支持翻译
     * 
     * @param sourceLanguage 源语言代码
     * @param targetLanguage 目标语言代码
     * @return 是否支持翻译
     */
    Result<Boolean> checkLanguagePairSupport(String sourceLanguage, String targetLanguage);
}
