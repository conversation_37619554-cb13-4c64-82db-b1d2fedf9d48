package com.nacos.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nacos.entity.po.ProviderCapabilityPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 视频翻译语种支持配置Mapper接口
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Mapper
public interface ProviderCapabilityMapper extends BaseMapper<ProviderCapabilityPO> {

    /**
     * 根据语言查询支持的视频翻译服务商
     */
    @Select("SELECT vls.* FROM yhc_video_translation_language_support vls " +
            "JOIN yhc_sys_provider p ON vls.provider_code = p.code " +
            "WHERE vls.language_code = #{languageCode} " +
            "AND vls.enabled = 1 AND p.enabled = 1 " +
            "ORDER BY p.priority DESC")
    List<ProviderCapabilityPO> selectByLanguage(@Param("languageCode") String languageCode);

    /**
     * 查询所有支持的视频翻译语种
     */
    @Select("SELECT DISTINCT vls.language_code " +
            "FROM yhc_video_translation_language_support vls " +
            "JOIN yhc_sys_provider p ON vls.provider_code = p.code " +
            "WHERE vls.enabled = 1 AND p.enabled = 1 " +
            "ORDER BY vls.language_code")
    List<String> selectAllSupportedLanguages();

    /**
     * 根据服务商代码查询其支持的所有视频翻译语种
     */
    @Select("SELECT * FROM yhc_video_translation_language_support " +
            "WHERE provider_code = #{providerCode} AND enabled = 1 " +
            "ORDER BY language_code")
    List<ProviderCapabilityPO> selectByProviderCode(@Param("providerCode") String providerCode);

    /**
     * 查询支持指定语言对翻译的服务商（源语言和目标语言都支持）
     */
    @Select("SELECT DISTINCT p.code, p.name, p.priority " +
            "FROM yhc_sys_provider p " +
            "WHERE p.enabled = 1 " +
            "AND EXISTS (SELECT 1 FROM yhc_video_translation_language_support vls1 " +
            "           WHERE vls1.provider_code = p.code AND vls1.language_code = #{sourceLanguage} AND vls1.enabled = 1) " +
            "AND EXISTS (SELECT 1 FROM yhc_video_translation_language_support vls2 " +
            "           WHERE vls2.provider_code = p.code AND vls2.language_code = #{targetLanguage} AND vls2.enabled = 1) " +
            "ORDER BY p.priority DESC")
    List<ProviderCapabilityPO> selectProvidersByLanguagePair(
            @Param("sourceLanguage") String sourceLanguage,
            @Param("targetLanguage") String targetLanguage);

    /**
     * 根据服务商代码和语言查询配置
     */
    @Select("SELECT * FROM yhc_video_translation_language_support " +
            "WHERE provider_code = #{providerCode} " +
            "AND language_code = #{languageCode} " +
            "AND enabled = 1")
    ProviderCapabilityPO selectByProviderAndLanguage(
            @Param("providerCode") String providerCode,
            @Param("languageCode") String languageCode);

    /**
     * 查询所有启用的视频翻译服务商配置
     */
    @Select("SELECT vls.* FROM yhc_video_translation_language_support vls " +
            "JOIN yhc_sys_provider p ON vls.provider_code = p.code " +
            "WHERE vls.enabled = 1 AND p.enabled = 1 " +
            "ORDER BY p.priority DESC")
    List<ProviderCapabilityPO> selectAllEnabledProviders();

    /**
     * 统计视频翻译语种支持数量
     */
    @Select("SELECT COUNT(*) FROM yhc_video_translation_language_support vls " +
            "JOIN yhc_sys_provider p ON vls.provider_code = p.code " +
            "WHERE vls.enabled = 1 AND p.enabled = 1")
    int countSupportedLanguages();

    /**
     * 检查语言对是否被支持（源语言和目标语言都有服务商支持）
     */
    @Select("SELECT COUNT(*) > 0 FROM yhc_sys_provider p " +
            "WHERE p.enabled = 1 " +
            "AND EXISTS (SELECT 1 FROM yhc_video_translation_language_support vls1 " +
            "           WHERE vls1.provider_code = p.code AND vls1.language_code = #{sourceLanguage} AND vls1.enabled = 1) " +
            "AND EXISTS (SELECT 1 FROM yhc_video_translation_language_support vls2 " +
            "           WHERE vls2.provider_code = p.code AND vls2.language_code = #{targetLanguage} AND vls2.enabled = 1)")
    boolean isLanguagePairSupported(
            @Param("sourceLanguage") String sourceLanguage,
            @Param("targetLanguage") String targetLanguage);

    /**
     * 批量更新启用状态
     */
    @Select("<script>" +
            "UPDATE yhc_video_translation_language_support SET enabled = #{enabled}, updated_time = NOW() " +
            "WHERE id IN " +
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    int updateEnabledStatus(@Param("ids") List<Long> ids, @Param("enabled") Integer enabled);

    /**
     * 更新服务商语言代码映射
     */
    @Select("UPDATE yhc_video_translation_language_support " +
            "SET provider_language_code = #{providerLanguageCode}, updated_time = NOW() " +
            "WHERE id = #{id}")
    int updateProviderLanguageCode(@Param("id") Long id,
                                  @Param("providerLanguageCode") String providerLanguageCode);
}
