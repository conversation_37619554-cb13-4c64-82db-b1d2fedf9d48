package com.nacos.model.SoundView.model.response;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 羚羊平台语种列表查询响应模型
 * 对应API: /open/v1/language/list
 * 
 * <AUTHOR>
 * @since 2025-01-25
 */
@Data
@Schema(description = "语种列表查询响应结果")
public class LanguageListResponseBO {

    /**
     * 返回码
     */
    @Schema(description = "返回码", example = "0")
    @JSONField(name = "code")
    private Integer code;

    /**
     * 返回信息
     */
    @Schema(description = "返回信息", example = "SUCCESS")
    @JSONField(name = "message")
    private String message;

    /**
     * 返回结果 - 直接是语种信息列表
     */
    @Schema(description = "返回结果")
    @JSONField(name = "result")
    private List<LanguageInfo> result;

    /**
     * 语种信息静态类
     */
    @Data
    @Schema(description = "语种信息")
    public static class LanguageInfo {

        /**
         * 语种代码
         */
        @Schema(description = "语种代码", example = "cn")
        @JSONField(name = "value")  // 根据实际API响应，字段名是 "value"
        private String code;

        /**
         * 语种名称
         */
        @Schema(description = "语种名称", example = "中文")
        @JSONField(name = "name")
        private String name;

        /**
         * 图标URL
         */
        @Schema(description = "图标URL")
        @JSONField(name = "icon")
        private String icon;

        /**
         * 排序权重
         */
        @Schema(description = "排序权重", example = "1")
        @JSONField(name = "sort")
        private Integer sortOrder;

        // ==================== 业务方法 ====================

        /**
         * 获取显示名称
         */
        public String getDisplayName() {
            return name != null && !name.trim().isEmpty() ? name : code;
        }
    }

    // ==================== 业务方法 ====================

    /**
     * 检查响应是否成功
     */
    public boolean isSuccess() {
        return code != null && code == 0;
    }

    /**
     * 检查是否有错误
     */
    public boolean hasError() {
        return code == null || code != 0;
    }

    /**
     * 获取错误信息
     */
    public String getErrorMessage() {
        if (isSuccess()) {
            return null;
        }
        return message != null ? message : "未知错误";
    }

    /**
     * 获取语种列表
     */
    public List<LanguageInfo> getLanguageList() {
        return result;
    }

    /**
     * 获取语种总数
     */
    public Integer getTotal() {
        return result != null ? result.size() : 0;
    }

    /**
     * 检查是否有数据
     */
    public boolean hasData() {
        return isSuccess() && result != null && !result.isEmpty();
    }

    /**
     * 根据代码查找语种
     */
    public LanguageInfo findLanguageByCode(String code) {
        if (result == null || code == null) {
            return null;
        }
        return result.stream()
                .filter(lang -> code.equals(lang.getCode()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取响应摘要信息
     */
    public String getSummary() {
        if (isSuccess()) {
            return String.format("语种列表查询成功: 共%d种语言", getTotal());
        } else {
            return String.format("语种列表查询失败: code=%d, message=%s", code, message);
        }
    }

    /**
     * 创建成功响应
     */
    public static LanguageListResponseBO success(List<LanguageInfo> languages) {
        LanguageListResponseBO response = new LanguageListResponseBO();
        response.setCode(0);
        response.setMessage("SUCCESS");
        response.setResult(languages);
        return response;
    }

    /**
     * 创建失败响应
     */
    public static LanguageListResponseBO failure(Integer code, String message) {
        LanguageListResponseBO response = new LanguageListResponseBO();
        response.setCode(code);
        response.setMessage(message);
        return response;
    }

    @Override
    public String toString() {
        return "LanguageListResponseBO{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", result=" + result +
                '}';
    }
}
