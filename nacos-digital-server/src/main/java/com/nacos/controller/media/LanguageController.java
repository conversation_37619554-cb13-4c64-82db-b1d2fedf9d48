package com.nacos.controller.media;

import com.nacos.entity.vo.LanguageVO;
import com.nacos.result.Result;
import com.nacos.service.LanguageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 语种支持控制器
 * 提供视频翻译语种支持相关的前端接口
 *
 * <AUTHOR>
 * @since 2025-01-05
 */
@Slf4j
@Tag(name = "语种支持", description = "视频翻译语种支持相关接口")
@RestController
@RequestMapping("/api/v1/media/language")
@Validated
@RequiredArgsConstructor
public class LanguageController {

    private final LanguageService languageService;

    /**
     * 获取支持的翻译语种列表
     * URL: /api/v1/media/language/supported-languages
     * Method: GET
     *
     * @param providerCode 服务商代码（可选，用于过滤特定服务商支持的语种）
     * @param popular 是否只返回热门语种（可选）
     * @return 支持的语种列表
     */
    @Operation(summary = "获取支持的翻译语种列表", 
               description = "获取所有支持视频翻译的语种列表，可按服务商和热门程度过滤")
    @GetMapping("/supported-languages")
    public Result<List<LanguageVO>> getSupportedLanguages(
            @Parameter(description = "服务商代码（可选）", example = "LINGYANG")
            @RequestParam(required = false) String providerCode,
            @Parameter(description = "是否只返回热门语种", example = "false")
            @RequestParam(required = false, defaultValue = "false") Boolean popular) {
        
        String methodName = "getSupportedLanguages";
        log.info("[{}] 获取支持的翻译语种列表, providerCode: {}, popular: {}", 
                methodName, providerCode, popular);
        
        try {
            Result<List<LanguageVO>> result = languageService.getSupportedLanguages(providerCode, popular);
            
            if (result.isSuccess()) {
                log.info("[{}] 获取语种列表成功, 共{}种语言", methodName, result.getData().size());
            } else {
                log.error("[{}] 获取语种列表失败: {}", methodName, result.getMessage());
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("[{}] 获取语种列表异常", methodName, e);
            return Result.ERROR("获取语种列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取热门翻译语种列表
     * URL: /api/v1/media/language/popular-languages
     * Method: GET
     *
     * @param limit 返回数量限制（默认10个）
     * @return 热门语种列表
     */
    @Operation(summary = "获取热门翻译语种列表", 
               description = "获取热门的翻译语种列表，按使用频率排序")
    @GetMapping("/popular-languages")
    public Result<List<LanguageVO>> getPopularLanguages(
            @Parameter(description = "返回数量限制", example = "10")
            @RequestParam(required = false, defaultValue = "10") Integer limit) {
        
        String methodName = "getPopularLanguages";
        log.info("[{}] 获取热门翻译语种列表, limit: {}", methodName, limit);
        
        try {
            Result<List<LanguageVO>> result = languageService.getPopularLanguages(limit);
            
            if (result.isSuccess()) {
                log.info("[{}] 获取热门语种列表成功, 共{}种语言", methodName, result.getData().size());
            } else {
                log.error("[{}] 获取热门语种列表失败: {}", methodName, result.getMessage());
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("[{}] 获取热门语种列表异常", methodName, e);
            return Result.ERROR("获取热门语种列表失败: " + e.getMessage());
        }
    }



    /**
     * 检查语言对是否支持翻译
     * URL: /api/v1/media/language/check-support
     * Method: GET
     *
     * @param sourceLanguage 源语言代码
     * @param targetLanguage 目标语言代码
     * @return 是否支持翻译
     */
    @Operation(summary = "检查语言对是否支持翻译", 
               description = "检查指定的源语言和目标语言之间是否支持翻译")
    @GetMapping("/check-support")
    public Result<Boolean> checkLanguagePairSupport(
            @Parameter(description = "源语言代码", example = "cn", required = true)
            @RequestParam String sourceLanguage,
            @Parameter(description = "目标语言代码", example = "en", required = true)
            @RequestParam String targetLanguage) {
        
        String methodName = "checkLanguagePairSupport";
        log.info("[{}] 检查语言对支持, sourceLanguage: {}, targetLanguage: {}", 
                methodName, sourceLanguage, targetLanguage);
        
        try {
            Result<Boolean> result = languageService.checkLanguagePairSupport(sourceLanguage, targetLanguage);
            
            log.info("[{}] 语言对支持检查完成, 结果: {}", methodName, result.getData());
            return result;
            
        } catch (Exception e) {
            log.error("[{}] 检查语言对支持异常", methodName, e);
            return Result.ERROR("检查语言对支持失败: " + e.getMessage());
        }
    }
}
