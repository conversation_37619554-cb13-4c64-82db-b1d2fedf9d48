package com.nacos.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * 视频翻译配置类
 * 
 * <p>管理视频翻译服务的所有配置参数，包括服务商配置、超时设置、重试策略等。
 * 支持多服务商配置和动态启用/禁用功能。</p>
 * 
 * <h3>配置结构</h3>
 * <pre>
 * digital:
 *   video:
 *     translate:
 *       default-provider: LINGYANG
 *       fallback-enabled: true
 *       providers:
 *         lingyang:
 *           enabled: true
 *           priority: 1
 *           timeout-minutes: 30
 *         aliyun:
 *           enabled: false
 *           priority: 2
 *           timeout-minutes: 25
 * </pre>
 * 
 * <AUTHOR>
 * @since 2025-01-30
 * @version 1.0
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "digital.video.translate")
public class VideoTranslateConfig {

    /**
     * 默认服务商
     */
    private String defaultProvider = "LINGYANG";

    /**
     * 是否启用故障转移
     */
    private boolean fallbackEnabled = true;

    /**
     * 全局超时时间（分钟）
     */
    private int globalTimeoutMinutes = 30;

    /**
     * 轮询间隔（毫秒）
     */
    private long pollIntervalMs = 5000;

    /**
     * 最大重试次数
     */
    private int maxRetries = 60;

    /**
     * 服务商配置映射
     */
    private Map<String, ProviderConfig> providers = new HashMap<>();

    /**
     * 服务商配置类
     */
    @Data
    public static class ProviderConfig {
        
        /**
         * 是否启用
         */
        private boolean enabled = true;
        
        /**
         * 优先级（数值越小优先级越高）
         */
        private int priority = 1;
        
        /**
         * 超时时间（分钟）
         */
        private int timeoutMinutes = 30;
        
        /**
         * 服务商描述
         */
        private String description = "";
        
        /**
         * 最大并发数
         */
        private int maxConcurrent = 5;
        
        /**
         * 重试次数
         */
        private int retryCount = 3;
        
        /**
         * 是否启用健康检查
         */
        private boolean healthCheckEnabled = true;
    }

    /**
     * 获取指定服务商的配置
     * 
     * @param provider 服务商名称
     * @return 服务商配置，如果不存在则返回默认配置
     */
    public ProviderConfig getProviderConfig(String provider) {
        if (provider == null || provider.trim().isEmpty()) {
            return createDefaultProviderConfig();
        }
        
        String upperProvider = provider.toUpperCase();
        return providers.getOrDefault(upperProvider, createDefaultProviderConfig());
    }

    /**
     * 检查指定服务商是否启用
     * 
     * @param provider 服务商名称
     * @return true表示启用，false表示禁用
     */
    public boolean isProviderEnabled(String provider) {
        ProviderConfig config = getProviderConfig(provider);
        return config.isEnabled();
    }

    /**
     * 获取所有启用的服务商配置
     * 
     * @return 启用的服务商配置映射
     */
    public Map<String, ProviderConfig> getEnabledProviders() {
        Map<String, ProviderConfig> enabledProviders = new HashMap<>();
        
        for (Map.Entry<String, ProviderConfig> entry : providers.entrySet()) {
            if (entry.getValue().isEnabled()) {
                enabledProviders.put(entry.getKey(), entry.getValue());
            }
        }
        
        return enabledProviders;
    }

    /**
     * 获取指定服务商的超时时间（毫秒）
     * 
     * @param provider 服务商名称
     * @return 超时时间（毫秒）
     */
    public long getProviderTimeoutMs(String provider) {
        ProviderConfig config = getProviderConfig(provider);
        return config.getTimeoutMinutes() * 60 * 1000L;
    }

    /**
     * 获取指定服务商的优先级
     * 
     * @param provider 服务商名称
     * @return 优先级
     */
    public int getProviderPriority(String provider) {
        ProviderConfig config = getProviderConfig(provider);
        return config.getPriority();
    }

    /**
     * 创建默认的服务商配置
     * 
     * @return 默认配置
     */
    private ProviderConfig createDefaultProviderConfig() {
        ProviderConfig config = new ProviderConfig();
        config.setEnabled(true);
        config.setPriority(99);
        config.setTimeoutMinutes(30);
        config.setDescription("默认配置");
        config.setMaxConcurrent(5);
        config.setRetryCount(3);
        config.setHealthCheckEnabled(true);
        return config;
    }

    /**
     * 初始化默认配置
     * 在Spring容器启动后自动调用
     */
    public void initDefaultConfigs() {
        // 确保羚羊平台配置存在
        if (!providers.containsKey("LINGYANG")) {
            ProviderConfig lingyangConfig = new ProviderConfig();
            lingyangConfig.setEnabled(true);
            lingyangConfig.setPriority(1);
            lingyangConfig.setTimeoutMinutes(30);
            lingyangConfig.setDescription("羚羊平台视频翻译服务");
            lingyangConfig.setMaxConcurrent(5);
            lingyangConfig.setRetryCount(3);
            lingyangConfig.setHealthCheckEnabled(true);
            providers.put("LINGYANG", lingyangConfig);
        }

        // 确保声音库翻译配置存在
        if (!providers.containsKey("VOICE_LIBRARY")) {
            ProviderConfig voiceLibraryConfig = new ProviderConfig();
            voiceLibraryConfig.setEnabled(true);
            voiceLibraryConfig.setPriority(10);
            voiceLibraryConfig.setTimeoutMinutes(60); // 声音库翻译需要更长时间
            voiceLibraryConfig.setDescription("声音库视频翻译服务");
            voiceLibraryConfig.setMaxConcurrent(3);
            voiceLibraryConfig.setRetryCount(2);
            voiceLibraryConfig.setHealthCheckEnabled(true);
            providers.put("VOICE_LIBRARY", voiceLibraryConfig);
        }

        // 确保阿里云配置存在
        if (!providers.containsKey("ALIYUN")) {
            ProviderConfig aliyunConfig = new ProviderConfig();
            aliyunConfig.setEnabled(false);
            aliyunConfig.setPriority(2);
            aliyunConfig.setTimeoutMinutes(25);
            aliyunConfig.setDescription("阿里云视频翻译服务");
            aliyunConfig.setMaxConcurrent(3);
            aliyunConfig.setRetryCount(2);
            aliyunConfig.setHealthCheckEnabled(true);
            providers.put("ALIYUN", aliyunConfig);
        }
    }

    /**
     * 验证配置的有效性
     * 
     * @return 验证结果，true表示配置有效
     */
    public boolean validateConfig() {
        // 检查默认服务商是否存在且启用
        if (defaultProvider == null || defaultProvider.trim().isEmpty()) {
            return false;
        }
        
        if (!isProviderEnabled(defaultProvider)) {
            return false;
        }
        
        // 检查是否至少有一个启用的服务商
        return !getEnabledProviders().isEmpty();
    }
}
