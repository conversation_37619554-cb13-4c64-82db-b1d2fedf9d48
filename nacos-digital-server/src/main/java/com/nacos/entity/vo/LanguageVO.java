package com.nacos.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 语种信息VO
 * 用于前端展示支持的翻译语种
 *
 * <AUTHOR>
 * @since 2025-01-05
 */
@Data
@Schema(description = "语种信息")
public class LanguageVO {

    /**
     * 语种代码
     */
    @Schema(description = "语种代码", example = "cn")
    private String code;

    /**
     * 语种名称
     */
    @Schema(description = "语种名称", example = "中文")
    private String name;

    /**
     * 语种英文名称
     */
    @Schema(description = "语种英文名称", example = "Chinese")
    private String englishName;

    /**
     * 图标URL
     */
    @Schema(description = "图标URL")
    private String icon;

    /**
     * 语种描述
     */
    @Schema(description = "语种描述", example = "中文（简体）")
    private String description;

    /**
     * 是否为热门语种
     */
    @Schema(description = "是否为热门语种", example = "true")
    private Boolean isPopular;

    /**
     * 排序权重
     */
    @Schema(description = "排序权重", example = "1")
    private Integer sortOrder;

    /**
     * 支持的服务商列表
     */
    @Schema(description = "支持该语种的服务商列表")
    private List<String> supportedProviders;

    // ==================== 业务方法 ====================

    /**
     * 获取显示名称（优先中文名称）
     */
    public String getDisplayName() {
        if (name != null && !name.trim().isEmpty()) {
            return name;
        } else if (englishName != null && !englishName.trim().isEmpty()) {
            return englishName;
        } else {
            return code;
        }
    }

    /**
     * 获取完整描述
     */
    public String getFullDescription() {
        StringBuilder sb = new StringBuilder();

        sb.append(getDisplayName());

        if (code != null && !code.trim().isEmpty()) {
            sb.append(" (").append(code).append(")");
        }

        if (description != null && !description.trim().isEmpty()) {
            sb.append(" - ").append(description);
        }

        return sb.toString();
    }

    /**
     * 检查是否支持指定服务商
     */
    public boolean supportsProvider(String providerCode) {
        return supportedProviders != null && supportedProviders.contains(providerCode);
    }

    /**
     * 获取服务商数量
     */
    public int getProviderCount() {
        return supportedProviders != null ? supportedProviders.size() : 0;
    }

    /**
     * 是否有多个服务商支持
     */
    public boolean hasMultipleProviders() {
        return getProviderCount() > 1;
    }

    /**
     * 获取支持的服务商描述
     */
    public String getSupportedProvidersDescription() {
        if (supportedProviders == null || supportedProviders.isEmpty()) {
            return "无";
        }
        return String.join("、", supportedProviders);
    }
}
