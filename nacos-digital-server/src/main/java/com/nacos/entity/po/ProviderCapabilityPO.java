package com.nacos.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 视频翻译语种支持配置实体类
 * 对应表：yhc_video_translation_language_support
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
@TableName("yhc_video_translation_language_support")
@Schema(description = "视频翻译语种支持配置实体类")
public class ProviderCapabilityPO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 服务商代码
     */
    @Schema(description = "服务商代码", example = "LINGYANG")
    @TableField("provider_code")
    private String providerCode;

    /**
     * 支持的语言代码
     */
    @Schema(description = "支持的语言代码", example = "cn")
    @TableField("language_code")
    private String languageCode;

    /**
     * 服务商内部语言代码
     */
    @Schema(description = "服务商内部语言代码", example = "zh-Hans")
    @TableField("provider_language_code")
    private String providerLanguageCode;



    /**
     * 是否启用：1启用 0禁用
     */
    @Schema(description = "是否启用：1启用 0禁用", example = "1")
    @TableField("enabled")
    private Integer enabled;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField("created_time")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField("updated_time")
    private LocalDateTime updatedTime;

    // ==================== 业务方法 ====================

    /**
     * 检查是否启用
     */
    public boolean isEnabledStatus() {
        return enabled != null && enabled == 1;
    }

    /**
     * 设置启用状态
     */
    public ProviderCapabilityPO setEnabledStatus(boolean enabledStatus) {
        this.enabled = enabledStatus ? 1 : 0;
        return this;
    }

    /**
     * 获取语言描述
     */
    public String getLanguageDescription() {
        return languageCode;
    }

    /**
     * 获取服务商语言描述
     */
    public String getProviderLanguageDescription() {
        return providerLanguageCode != null ? providerLanguageCode : languageCode;
    }

    /**
     * 检查语言是否匹配
     */
    public boolean matchesLanguage(String languageCode) {
        return this.languageCode != null && this.languageCode.equals(languageCode);
    }

    /**
     * 检查是否支持视频翻译（专用方法）
     */
    public boolean supportsVideoTranslation() {
        return enabled != null && enabled == 1;
    }

    /**
     * 获取有效的语言代码（优先使用服务商内部代码）
     */
    public String getEffectiveLanguageCode() {
        return providerLanguageCode != null && !providerLanguageCode.trim().isEmpty()
               ? providerLanguageCode : languageCode;
    }

    @Override
    public String toString() {
        return "ProviderCapabilityPO{" +
                "id=" + id +
                ", providerCode='" + providerCode + '\'' +
                ", languageCode='" + languageCode + '\'' +
                ", providerLanguageCode='" + providerLanguageCode + '\'' +
                ", enabled=" + enabled +
                '}';
    }
}
