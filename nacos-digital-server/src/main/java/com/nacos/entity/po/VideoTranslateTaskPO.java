package com.nacos.entity.po;

import com.baomidou.mybatisplus.annotation.*;
import com.nacos.entity.enums.UnifiedTaskStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 视频翻译任务实体类（简化版）
 * 根据最新DDL结构优化，只保留必要字段，其他信息存储在JSON字段中
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
@TableName("digital_video_translation_task")
@Schema(description = "视频翻译任务实体类")
public class VideoTranslateTaskPO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务唯一标识
     */
    @Schema(description = "任务唯一标识")
    @TableField("task_id")
    private String taskId;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    @TableField("user_id")
    private String userId;

    /**
     * 任务名称
     */
    @Schema(description = "任务名称")
    @TableField("task_name")
    private String taskName;

    /**
     * 服务商标识
     */
    @Schema(description = "服务商标识", example = "AZURE")
    @TableField("provider")
    private String provider;

    /**
     * 视频封面URL
     */
    @Schema(description = "视频封面URL")
    @TableField("cover_url")
    private String coverUrl;

    /**
     * 源视频URL
     */
    @Schema(description = "源视频URL")
    @TableField("source_video_url")
    private String sourceVideoUrl;

    /**
     * 翻译后的视频URL
     */
    @Schema(description = "翻译后的视频URL")
    @TableField("translated_video_url")
    private String translatedVideoUrl;

    /**
     * 翻译后的音频URL
     */
    @Schema(description = "翻译后的音频URL")
    @TableField("translated_audio_url")
    private String translatedAudioUrl;

    /**
     * 状态：使用统一状态枚举
     * 0-失败 1-成功 2-排队中 3-进行中 4-超时 5-已取消
     * 对应UnifiedTaskStatusEnum: FAILED(0), SUCCESS(1), QUEUING(2), PROGRESS(3), TIMEOUT(4), CANCELLED(5)
     */
    @Schema(description = "状态：0-失败 1-成功 2-排队中 3-进行中 4-超时 5-已取消")
    @TableField("status")
    private Integer status;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    @TableField("error_msg")
    private String errorMsg;

    /**
     * 翻译视频的时长 (毫秒)
     */
    @Schema(description = "翻译视频的时长 (毫秒)")
    @TableField("duration_ms")
    private Integer durationMs;

    /**
     * 翻译类型：原声翻译/声音库翻译
     */
    @Schema(description = "翻译类型：原声翻译/声音库翻译", example = "原声翻译")
    @TableField("translation_type")
    private String translationType;

    /**
     * 视频翻译请求参数 (JSON格式，包含Azure特定字段)
     */
    @Schema(description = "视频翻译请求参数 (JSON格式，包含Azure特定字段)")
    @TableField("request_params_json")
    private String requestParamsJson;

    /**
     * 翻译结果详情 (JSON格式)
     */
    @Schema(description = "翻译结果详情 (JSON格式)")
    @TableField("result_json")
    private String resultJson;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField("created_time")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 是否删除：0-未删除 1-已删除
     */
    @Schema(description = "是否删除：0-未删除 1-已删除")
    @TableField("is_deleted")
    @TableLogic
    private Integer isDeleted;

    // ==================== 业务方法 ====================

    /**
     * 检查任务是否处理中
     * 新状态：QUEUING(2) 或 PROGRESS(3)
     */
    public boolean isProcessing() {
        return status != null && (status == 2 || status == 3);
    }

    /**
     * 检查任务是否已完成
     * 新状态：SUCCESS(1)
     */
    public boolean isCompleted() {
        return status != null && status == 1;
    }

    /**
     * 检查任务是否失败
     * 新状态：FAILED(0) 或 TIMEOUT(4)
     */
    public boolean isFailed() {
        return status != null && (status == 0 || status == 4);
    }

    /**
     * 检查任务是否已取消
     * 新状态：CANCELLED(5)
     */
    public boolean isCancelled() {
        return status != null && status == 5;
    }

    /**
     * 检查任务是否为最终状态
     * 最终状态：SUCCESS(1), FAILED(0), TIMEOUT(4), CANCELLED(5)
     */
    public boolean isFinalStatus() {
        return status != null && (status == 0 || status == 1 || status == 4 || status == 5);
    }

    /**
     * 获取状态描述
     * 新状态映射：0-失败 1-成功 2-排队中 3-进行中 4-超时 5-已取消
     */
    public String getStatusDescription() {
        if (status == null) {
            return "未知状态";
        }

        switch (status) {
            case 0: return "失败";
            case 1: return "翻译成功";
            case 2: return "排队中";
            case 3: return "进行中";
            case 4: return "超时";
            case 5: return "已取消";
            default: return "未知状态";
        }
    }

    /**
     * 检查是否有结果文件
     */
    public boolean hasResultFiles() {
        return (translatedVideoUrl != null && !translatedVideoUrl.trim().isEmpty()) ||
               (translatedAudioUrl != null && !translatedAudioUrl.trim().isEmpty());
    }

    /**
     * 检查是否有视频结果
     */
    public boolean hasVideoResult() {
        return translatedVideoUrl != null && !translatedVideoUrl.trim().isEmpty();
    }

    /**
     * 检查是否有音频结果
     */
    public boolean hasAudioResult() {
        return translatedAudioUrl != null && !translatedAudioUrl.trim().isEmpty();
    }

    /**
     * 获取任务摘要信息（包含翻译类型）
     */
    public String getTaskSummary() {
        return String.format("任务[%s]: %s, 状态: %s, 服务商: %s, 类型: %s",
            taskId,
            taskName != null ? taskName : "视频翻译",
            getStatusDescription(),
            provider != null ? provider : "未知",
            translationType != null ? translationType : "未知");
    }

    /**
     * 获取时长描述（毫秒转换为可读格式）
     */
    public String getDurationDescription() {
        if (durationMs == null || durationMs <= 0) {
            return "未知";
        }

        long totalSeconds = durationMs / 1000;
        long minutes = totalSeconds / 60;
        long seconds = totalSeconds % 60;

        if (minutes > 0) {
            return minutes + "分" + seconds + "秒";
        } else {
            return seconds + "秒";
        }
    }

    // ==================== 统一状态枚举便捷方法 ====================

    /**
     * 获取统一状态枚举
     */
    public UnifiedTaskStatusEnum getUnifiedStatus() {
        return UnifiedTaskStatusEnum.fromCode(status);
    }

    /**
     * 设置统一状态枚举
     */
    public void setUnifiedStatus(UnifiedTaskStatusEnum unifiedStatus) {
        this.status = unifiedStatus != null ? unifiedStatus.getCode() : null;
    }

    /**
     * 检查是否可以转换到目标状态
     */
    public boolean canTransitionTo(UnifiedTaskStatusEnum targetStatus) {
        UnifiedTaskStatusEnum currentStatus = getUnifiedStatus();
        return currentStatus != null && currentStatus.canTransitionTo(targetStatus);
    }

    /**
     * 获取状态的显示样式（用于前端展示）
     */
    public String getStatusDisplayStyle() {
        UnifiedTaskStatusEnum unifiedStatus = getUnifiedStatus();
        return unifiedStatus != null ? unifiedStatus.getDisplayStyle() : "default";
    }

    // ==================== 翻译类型相关方法 ====================

    /**
     * 翻译类型常量
     */
    public static final String TRANSLATION_TYPE_ORIGINAL = "原声翻译";
    public static final String TRANSLATION_TYPE_VOICE_LIBRARY = "声音库翻译";

    /**
     * 检查是否为原声翻译
     */
    public boolean isOriginalVoiceTranslation() {
        return TRANSLATION_TYPE_ORIGINAL.equals(translationType);
    }

    /**
     * 检查是否为声音库翻译
     */
    public boolean isVoiceLibraryTranslation() {
        return TRANSLATION_TYPE_VOICE_LIBRARY.equals(translationType);
    }

    /**
     * 根据useOriginalVoice参数设置翻译类型
     */
    public void setTranslationTypeByFlag(Boolean useOriginalVoice) {
        if (Boolean.TRUE.equals(useOriginalVoice)) {
            this.translationType = TRANSLATION_TYPE_ORIGINAL;
        } else {
            this.translationType = TRANSLATION_TYPE_VOICE_LIBRARY;
        }
    }

    /**
     * 获取翻译类型的英文标识（用于日志和API）
     */
    public String getTranslationTypeCode() {
        if (TRANSLATION_TYPE_ORIGINAL.equals(translationType)) {
            return "ORIGINAL_VOICE";
        } else if (TRANSLATION_TYPE_VOICE_LIBRARY.equals(translationType)) {
            return "VOICE_LIBRARY";
        } else {
            return "UNKNOWN";
        }
    }

}
